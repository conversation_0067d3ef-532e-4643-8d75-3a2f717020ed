{"name": "ragtop-web", "version": "0.0.1", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@ragtop-web/eslint-config": "workspace:*", "@ragtop-web/typescript-config": "workspace:*", "prettier": "^3.5.1", "turbo": "^2.4.2", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-scroll-area": "1.2.8", "@tanstack/react-query": "5.76.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.7.0", "radix-ui": "1.4.2", "react-hook-form": "7.56.3", "tailwind-merge": "^3.0.1", "zod": "^3.24.2"}}