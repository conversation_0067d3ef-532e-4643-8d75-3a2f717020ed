# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab_api-ci.yml` file.
# You should not add this template to an existing `.gitlab_api-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

stages: # List of stages for jobs, and their order of execution
  - build
  - tag

variables:
  GIT_DEPTH: 1000
  GIT_STRATEGY: clone
  TARGET_BRANCH_NAME: main
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ""
  NODE_OPTIONS: "--max-old-space-size=8192"
  APP_NAME: "ragtop-frontend"

.build-job-before:
  stage: build
  image: zbytecom/nodejs-builder:0.2.2
  tags:
    - cloud_tencent
    - specs_8c16g
  services:
    - docker:24.0.5-dind
  rules:
    - if: >-
        ($CI_PROJECT_PATH == "xiaoqian/ragtop-web") &&
        ($CI_COMMIT_BRANCH =~ /^main|(product|release)\/.+$/) &&
        ($CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != null)
      when: always
    - if: >-
        ($CI_PIPELINE_SOURCE == "push" ||
         $CI_PIPELINE_SOURCE == "merge_request_event")
      when: manual

  before_script: |
    set -xe
    docker info
    echo "node version" `node -v`
    echo "pnpm version" `pnpm -v`
    docker login zbyte.tencentcloudcr.com --username 100026519137 --password ${TCRKEY}
  interruptible: true


#================================================================
# Stage: build
#================================================================
job_for_tag:
  extends: .build-job-before
  allow_failure: true
  stage: build

  rules:
    - if: "$CI_COMMIT_TAG =~ /web|management/ "
      when: always

  script: |
    set -xe
     docker -v
     DOCKER_BUILD=true
     DOCKERFILE=Dockerfile
     FOLDER=release
     DEPLOY_PATH=apps/web/
     PACKAGE_NAME=web

    if [[ "$CI_COMMIT_TAG" =~ web ]]; then
      echo "Building web images"
    elif [[ "$CI_COMMIT_TAG" =~ management ]]; then
      echo "Building management images"
      DEPLOY_PATH=apps/management/
      PACKAGE_NAME=management
    fi

    echo "Start building ragtop-frontend image"

    if [[ "$CI_COMMIT_TAG" =~ staging ]]; then
      DOCKER_BUILD=false
      FOLDER=dev_daily
    fi

    time docker build \
       --build-arg CI_COMMIT_SHORT_SHA=$CI_COMMIT_SHORT_SHA \
       --build-arg CI_COMMIT_SHA=$CI_COMMIT_SHA \
       --build-arg PACKAGE_NAME=$PACKAGE_NAME \
       --build-arg DOCKER_BUILD=$DOCKER_BUILD \
       -f $DEPLOY_PATH$DOCKERFILE \
       -t $APP_NAME:$CI_COMMIT_SHA . 
    echo "Build image : zbyte.tencentcloudcr.com/$FOLDER/$APP_NAME:$CI_COMMIT_SHA"
    docker tag $APP_NAME:$CI_COMMIT_SHA zbyte.tencentcloudcr.com/$FOLDER/$APP_NAME:$CI_COMMIT_TAG
    time docker push zbyte.tencentcloudcr.com/$FOLDER/$APP_NAME:$CI_COMMIT_TAG
    echo "Finish everything, build image : zbyte.tencentcloudcr.com/$FOLDER/$APP_NAME:$CI_COMMIT_TAG"
