"use client"

import { useState, useEffect } from "react"
import { Search, File, Check } from "lucide-react"
import { Input } from "@ragtop-web/ui/components/input"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import { ScrollArea } from "@ragtop-web/ui/components/scroll-area"
import { Checkbox } from "@ragtop-web/ui/components/checkbox"
import { Label } from "@ragtop-web/ui/components/label"
import { Badge } from "@ragtop-web/ui/components/badge"

// 模拟文件数据
const mockFiles = [
  { id: "1", name: "产品说明书.pdf", type: "pdf", size: "2.4 MB" },
  { id: "2", name: "用户手册.pdf", type: "pdf", size: "3.1 MB" },
  { id: "3", name: "技术文档.pdf", type: "pdf", size: "1.8 MB" },
  { id: "4", name: "API文档.pdf", type: "pdf", size: "4.2 MB" },
  { id: "5", name: "安装指南.pdf", type: "pdf", size: "1.5 MB" },
  { id: "6", name: "常见问题.pdf", type: "pdf", size: "0.9 MB" },
  { id: "7", name: "系统架构.pdf", type: "pdf", size: "2.7 MB" },
  { id: "8", name: "更新日志.pdf", type: "pdf", size: "1.2 MB" },
  { id: "9", name: "数据字典.pdf", type: "pdf", size: "3.5 MB" },
  { id: "10", name: "安全白皮书.pdf", type: "pdf", size: "2.1 MB" },
]

export type FileItem = {
  id: string
  name: string
  type: string
  size: string
}

interface FileSelectorProps {
  selectedFiles: FileItem[]
  onChange: (files: FileItem[]) => void
  maxFiles?: number
}

export function FileSelector({ selectedFiles, onChange, maxFiles = 10 }: FileSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [files, setFiles] = useState<FileItem[]>(mockFiles)
  const [filteredFiles, setFilteredFiles] = useState<FileItem[]>(mockFiles)

  // 搜索文件
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredFiles(files)
    } else {
      const filtered = files.filter((file) =>
        file.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredFiles(filtered)
    }
  }, [searchTerm, files])

  // 处理文件选择
  const handleFileSelect = (file: FileItem) => {
    const isSelected = selectedFiles.some((f) => f.id === file.id)
    
    if (isSelected) {
      // 如果已选中，则取消选择
      onChange(selectedFiles.filter((f) => f.id !== file.id))
    } else {
      // 如果未选中且未超过最大数量，则添加选择
      if (selectedFiles.length < maxFiles) {
        onChange([...selectedFiles, file])
      }
    }
  }

  // 清除所有选择
  const handleClearSelection = () => {
    onChange([])
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索文件..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        {selectedFiles.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearSelection}
          >
            清除选择
          </Button>
        )}
      </div>

      {/* 已选文件显示 */}
      {selectedFiles.length > 0 && (
        <div className="flex flex-wrap gap-2 my-2">
          {selectedFiles.map((file) => (
            <Badge key={file.id} variant="secondary" className="flex items-center gap-1">
              <File className="h-3 w-3" />
              {file.name}
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 ml-1 p-0"
                onClick={() => handleFileSelect(file)}
              >
                <span className="sr-only">移除</span>
                <span className="text-xs">×</span>
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* 文件列表 */}
      <ScrollArea className="h-[300px] border rounded-md">
        <div className="p-4 space-y-2">
          {filteredFiles.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              未找到匹配的文件
            </div>
          ) : (
            filteredFiles.map((file) => {
              const isSelected = selectedFiles.some((f) => f.id === file.id)
              return (
                <div
                  key={file.id}
                  className={`flex items-center p-2 rounded-md hover:bg-muted cursor-pointer ${
                    isSelected ? "bg-muted" : ""
                  }`}
                  onClick={() => handleFileSelect(file)}
                >
                  <Checkbox
                    id={`file-${file.id}`}
                    checked={isSelected}
                    onCheckedChange={() => handleFileSelect(file)}
                    className="mr-2"
                  />
                  <Label
                    htmlFor={`file-${file.id}`}
                    className="flex-1 flex items-center cursor-pointer"
                  >
                    <File className="h-4 w-4 mr-2 text-primary" />
                    <div className="flex-1">
                      <div className="font-medium">{file.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {file.size}
                      </div>
                    </div>
                    {isSelected && (
                      <Check className="h-4 w-4 text-primary ml-2" />
                    )}
                  </Label>
                </div>
              )
            })
          )}
        </div>
      </ScrollArea>

      <div className="text-xs text-muted-foreground">
        已选择 {selectedFiles.length} 个文件 {maxFiles && `(最多 ${maxFiles} 个)`}
      </div>
    </div>
  )
}
