"use client"

import * as React from "react"
import { DataTable, type ColumnDef } from "./data-table"
import { Pagination } from "./pagination"
import { cn } from "../lib/utils"

/**
 * 分页表格的数据响应接口
 */
export interface PaginatedResponse<T> {
  /** 数据记录 */
  records: T[]
  /** 总条目数 */
  total: number
  /** 当前页码 */
  page_number?: number
  /** 每页大小 */
  page_size?: number
}

/**
 * 分页表格组件的属性
 */
export interface PaginatedTableProps<T> {
  /** 表格列定义 */
  columns: ColumnDef<T>[]
  /** 分页数据 */
  data: PaginatedResponse<T> | undefined
  /** 是否加载中 */
  isLoading?: boolean
  /** 当前页码 */
  currentPage: number
  /** 每页大小 */
  pageSize: number
  /** 页码变化回调 */
  onPageChange: (page: number) => void
  /** 每页大小变化回调 */
  onPageSizeChange?: (pageSize: number) => void
  /** 可选的每页大小选项 */
  pageSizeOptions?: number[]
  /** 是否显示总数信息 */
  showTotal?: boolean
  /** 是否显示每页大小选择器 */
  showPageSizeSelector?: boolean
  /** 加载状态的自定义内容 */
  loadingContent?: React.ReactNode
  /** 空状态的自定义内容 */
  emptyContent?: React.ReactNode
  /** 表格容器的额外类名 */
  className?: string
  /** 分页组件的额外类名 */
  paginationClassName?: string
}

/**
 * 默认加载状态组件
 */
const DefaultLoadingContent = () => (
  <div className="flex items-center justify-center h-64 border rounded-md">
    <div className="flex flex-col items-center gap-2">
      <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
      <p className="text-sm text-muted-foreground">加载中...</p>
    </div>
  </div>
)

/**
 * 默认空状态组件
 */
const DefaultEmptyContent = () => (
  <div className="flex items-center justify-center h-64 border rounded-md">
    <div className="flex flex-col items-center gap-2">
      <p className="text-sm text-muted-foreground">暂无数据</p>
    </div>
  </div>
)

/**
 * 分页表格组件
 * 
 * 集成了DataTable和Pagination组件，提供完整的分页表格功能
 * 
 * @param props 组件属性
 * @returns 分页表格组件
 */
export function PaginatedTable<T>({
  columns,
  data,
  isLoading = false,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showTotal = true,
  showPageSizeSelector = true,
  loadingContent,
  emptyContent,
  className,
  paginationClassName,
}: PaginatedTableProps<T>) {
  // 提取数据
  const records = data?.records || []
  const total = data?.total || 0
  const pageCount = Math.ceil(total / pageSize)

  // 渲染加载状态
  if (isLoading) {
    return loadingContent || <DefaultLoadingContent />
  }

  // 渲染空状态
  if (!isLoading && records.length === 0) {
    return emptyContent || <DefaultEmptyContent />
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 数据表格 */}
      <DataTable columns={columns} data={records} />
      
      {/* 分页组件 */}
      {(pageCount > 1 || showTotal || showPageSizeSelector) && (
        <div className={cn("flex justify-center", paginationClassName)}>
          <Pagination
            pageCount={pageCount}
            currentPage={currentPage}
            onPageChange={onPageChange}
            total={total}
            pageSize={pageSize}
            onPageSizeChange={onPageSizeChange}
            pageSizeOptions={pageSizeOptions}
            showTotal={showTotal}
            showPageSizeSelector={showPageSizeSelector}
          />
        </div>
      )}
    </div>
  )
}
