"use client"

import React from "react"
import { Search, Loader2 } from "lucide-react"
import { Input } from "./input"
import { Label } from "./label"
import { Checkbox } from "./checkbox"
import { RadioGroup, RadioGroupItem } from "./radio-group"
import { ScrollArea } from "./scroll-area"
import { cn } from "@ragtop-web/ui/lib/utils"

/**
 * 选择项接口
 */
export interface SelectionItem {
  id: string
  label: string
  disabled?: boolean
  [key: string]: any
}

/**
 * 选择列表组件属性接口
 */
export interface SelectionListProps {
  /** 数据列表 */
  items: SelectionItem[]
  /** 是否加载中 */
  isLoading?: boolean
  /** 是否还有更多数据 */
  hasMore?: boolean
  /** 搜索关键词 */
  searchTerm?: string
  /** 搜索关键词变化回调 */
  onSearchChange?: (term: string) => void
  /** 滚动事件回调 */
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void
  /** 选择模式：single 单选，multiple 多选 */
  selectionMode?: "single" | "multiple"
  /** 已选择的项目 ID */
  selectedIds?: string | string[]
  /** 选择变化回调 */
  onSelectionChange?: (selectedIds: string | string[]) => void
  /** 搜索框占位符 */
  searchPlaceholder?: string
  /** 空状态文本 */
  emptyText?: string
  /** 加载文本 */
  loadingText?: string
  /** 到底文本 */
  endText?: string
  /** 容器高度 */
  height?: string
  /** 是否显示搜索框 */
  showSearch?: boolean
  /** 自定义渲染项目 */
  renderItem?: (
    item: SelectionItem,
    isSelected: boolean,
    isDisabled: boolean,
    onSelect: (itemId: string) => void
  ) => React.ReactNode
  /** 额外的 CSS 类名 */
  className?: string
}

/**
 * 选择列表组件
 *
 * 支持单选和多选模式，内置搜索、分页加载功能
 */
export function SelectionList({
  items = [],
  isLoading = false,
  hasMore = false,
  searchTerm = "",
  onSearchChange,
  onScroll,
  selectionMode = "multiple",
  selectedIds = selectionMode === "single" ? "" : [],
  onSelectionChange,
  searchPlaceholder = "搜索...",
  emptyText = "未找到匹配项",
  loadingText = "加载中...",
  endText = "已到达列表底部",
  height = "300px",
  showSearch = true,
  renderItem,
  className
}: SelectionListProps) {

  /**
   * 处理单个项目选择
   */
  const handleItemSelect = (itemId: string, isDisabled: boolean) => {
    if (isDisabled || !onSelectionChange) return

    if (selectionMode === "single") {
      onSelectionChange(itemId)
    } else {
      const currentSelected = Array.isArray(selectedIds) ? selectedIds : []
      if (currentSelected.includes(itemId)) {
        onSelectionChange(currentSelected.filter(id => id !== itemId))
      } else {
        onSelectionChange([...currentSelected, itemId])
      }
    }
  }

  /**
   * 检查项目是否被选中
   */
  const isItemSelected = (itemId: string): boolean => {
    if (selectionMode === "single") {
      return selectedIds === itemId
    } else {
      return Array.isArray(selectedIds) && selectedIds.includes(itemId)
    }
  }

  /**
   * 渲染默认项目
   */
  const renderDefaultItem = (item: SelectionItem, isSelected: boolean, isDisabled: boolean) => {
    const itemContent = (
      <div className="flex items-center space-x-2 flex-1">
        <Label className="cursor-pointer flex-1">
          <span className={cn(isDisabled && "text-muted-foreground")}>
            {item.label}
          </span>
        </Label>
      </div>
    )

    if (selectionMode === "single") {
      return (
        <div
          key={item.id}
          className={cn(
            "flex items-center space-x-2 rounded-md p-2 cursor-pointer transition-colors",
            isSelected && "bg-accent",
            !isDisabled && "hover:bg-accent",
            isDisabled && "cursor-not-allowed opacity-50"
          )}
          onClick={() => handleItemSelect(item.id, isDisabled)}
        >
          <RadioGroupItem
            value={item.id}
            disabled={isDisabled}
            className="pointer-events-none"
          />
          {itemContent}
        </div>
      )
    } else {
      return (
        <div
          key={item.id}
          className={cn(
            "flex items-center space-x-2 rounded-md p-2 cursor-pointer transition-colors",
            isSelected && "bg-accent",
            !isDisabled && "hover:bg-accent",
            isDisabled && "cursor-not-allowed opacity-50"
          )}
          onClick={() => handleItemSelect(item.id, isDisabled)}
        >
          <Checkbox
            checked={isSelected}
            disabled={isDisabled}
            className="pointer-events-none"
          />
          {itemContent}
        </div>
      )
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* 搜索框 */}
      {showSearch && (
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={searchPlaceholder}
            className="pl-8"
            value={searchTerm}
            onChange={(e) => onSearchChange?.(e.target.value)}
          />
        </div>
      )}

      {/* 列表容器 */}
      <div className="border rounded-md">
        <ScrollArea style={{ height }} className="p-2">
          <div
            className="space-y-2"
            onScroll={onScroll}
          >
            {/* 单选模式需要 RadioGroup 包装 */}
            {selectionMode === "single" ? (
              <RadioGroup
                value={typeof selectedIds === "string" ? selectedIds : ""}
                onValueChange={(value) => onSelectionChange?.(value)}
              >
                {isLoading && items.length === 0 ? (
                  <div className="py-6 text-center">
                    <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">{loadingText}</p>
                  </div>
                ) : items.length === 0 ? (
                  <div className="py-6 text-center text-sm text-muted-foreground">
                    {emptyText}
                  </div>
                ) : (
                  items.map((item) => {
                    const isSelected = isItemSelected(item.id)
                    const isDisabled = item.disabled || false

                    return renderItem
                      ? renderItem(item, isSelected, isDisabled, (itemId) => handleItemSelect(itemId, isDisabled))
                      : renderDefaultItem(item, isSelected, isDisabled)
                  })
                )}
              </RadioGroup>
            ) : (
              <>
                {isLoading && items.length === 0 ? (
                  <div className="py-6 text-center">
                    <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">{loadingText}</p>
                  </div>
                ) : items.length === 0 ? (
                  <div className="py-6 text-center text-sm text-muted-foreground">
                    {emptyText}
                  </div>
                ) : (
                  items.map((item) => {
                    const isSelected = isItemSelected(item.id)
                    const isDisabled = item.disabled || false

                    return renderItem
                      ? renderItem(item, isSelected, isDisabled, (itemId) => handleItemSelect(itemId, isDisabled))
                      : renderDefaultItem(item, isSelected, isDisabled)
                  })
                )}
              </>
            )}

            {/* 加载更多指示器 */}
            {isLoading && items.length > 0 && (
              <div className="py-2 text-center">
                <Loader2 className="h-4 w-4 animate-spin mx-auto" />
              </div>
            )}

            {/* 到底提示 */}
            {!isLoading && !hasMore && items.length > 0 && (
              <div className="py-2 text-center text-sm text-muted-foreground">
                {endText}
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
