"use client"

import { toast as sonnerToast, type ToastT } from "sonner"

type ToastProps = Omit<ToastT, "id"> & {
  title?: string
  description?: string
  variant?: "default" | "destructive"
}

export function useToast() {
  return {
    toast: ({ title, description, variant, ...props }: ToastProps) => {
      if (variant === "destructive") {
        return sonnerToast.error(title, {
          description,
          ...props,
        })
      }

      return sonnerToast(title, {
        description,
        ...props,
      })
    },
  }
}
