/**
 * 用户服务
 *
 * 提供用户相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createApiClient } from '@ragtop-web/ui/lib/api'
// 导出API前缀常量
const API_PREFIX = process.env.NEXT_PUBLIC_API_PREFIX || '/api/v1/portal-ragtop-admin'

// 创建API客户端
const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/user`
})

const pageApiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/user`,
  isPaginated:true,
})

export interface CreateParams {
    loginName?: string;
    nick?: string;
    password?: string;
    [property: string]: any;
}

// 用户接口
export interface User {
  id: string
  name: string
  login_name?: string
  nick?: string
  email: string
  role: 'admin' | 'user'
  createdAt: string
}

/**
 * 获取用户列表（分页）
 *
 * @param pageNumber - 当前页码
 * @param pageSize - 每页条数
 * @param keyword - 搜索关键词
 */
export const useUsers = (pageNumber: number = 1, pageSize: number = 10, keyword?: string) => {
  return useQuery({
    queryKey: ['users', pageNumber, pageSize, keyword],
    queryFn: () => pageApiClient.post<{
      records: User[],
      total: number,
      page_number: number,
      page_size: number
    }>('/query', {
      keyword
    }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
  })
}

/**
 * 查询用户列表（用于选择器）
 */
export const useQueryUsers = () => {
  return useMutation({
    mutationFn: (params: {
      page_number?: number;
      page_size?: number;
      keyword?: string
    }) => pageApiClient.post<{
      records: User[],
      total: number,
      page_number: number,
      page_size: number
    }>('/query', params),
  })
}

/**
 * 获取当前用户
 */
export const useCurrentUser = () => {
   return useMutation({
    mutationFn: () =>
      apiClient.post<User>("/current"),
  })
}

/**
 * 获取用户详情
 */
export const useUser = (id: string) => {
    const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (userData: "user_id") =>
      apiClient.post<User>("/get", userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}


/**
 * 创建用户
 */
export const useCreateUser = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (userData:CreateParams) =>
      apiClient.post<User>('/create', userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}

/**
 * 更新用户
 */
export const useUpdateUser = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (userData:Partial<CreateParams> & {user_id: string}) =>
      apiClient.post<User>(`/update`, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}

/**
 * 删除用户
 */
export const useDeleteUser = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: {user_id: string}) => apiClient.post(`/delete`,data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}

