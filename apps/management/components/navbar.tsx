"use client"

import Link from "next/link"
import { ThemeToggle } from "@ragtop-web/ui/components/theme-toggle"

/**
 * 导航栏组件
 * 
 * 包含应用标题和主题切换按钮
 */
export function Navbar() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
      <div className="container flex h-14 items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/" className="font-semibold">
            Management Dashboard
          </Link>
        </div>
        <div className="flex items-center gap-2">
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}
