"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog"
import { usePasswordModify } from "@/service/auth-service"

// 表单验证模式
const formSchema = z.object({
  newPassword: z.string().min(6, "新密码至少需要6个字符"),
  confirmPassword: z.string().min(6, "确认密码至少需要6个字符"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "新密码与确认密码不匹配",
  path: ["confirmPassword"],
})

type FormValues = z.infer<typeof formSchema>

interface PasswordChangeDialogProps {
  open: boolean
  onClose: () => void
}

/**
 * 密码修改对话框组件
 */
export function PasswordChangeDialog({ open, onClose }: PasswordChangeDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState("")
    const passwordModifyMutation  = usePasswordModify()


  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  })

  // 处理密码修改
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true)
    setError("")

    passwordModifyMutation.mutate({new_password:values.newPassword}, {
      onSuccess: () => {
        setSuccess(true)
        form.reset()
        setTimeout(() => {
          onClose()
          setSuccess(false)
        }, 3000)
      },
      onError: (error) => {
        setError("修改密码失败")
        setIsLoading(false)
      },
    })
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>修改超级管理员密码</DialogTitle>
          <DialogDescription>
            请输入当前密码和新密码以完成修改
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="p-3 bg-destructive/15 text-destructive rounded-md text-sm">
            {error}
          </div>
        )}
        
        {success && (
          <div className="p-3 bg-green-500/15 text-green-500 rounded-md text-sm">
            密码修改成功！对话框将自动关闭...
          </div>
        )}
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>新密码</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="输入新密码" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>确认新密码</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="再次输入新密码" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                取消
              </Button>
              <Button type="submit" disabled={isLoading || success}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    提交中...
                  </span>
                ) : (
                  "确认修改"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
