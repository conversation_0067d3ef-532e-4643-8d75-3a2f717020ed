"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ragtop-web/ui/components/card"
import { LogIn } from "lucide-react"
import { useLogin } from "@/service/auth-service"
import { useAtomValue } from "jotai"
import { accessTokenAtom } from "@/store/user-store"

// 表单验证模式
const formSchema = z.object({
  username: z.string().min(2,"用户名至少需要2个字符"),
  password: z.string(),
})

type FormValues = z.infer<typeof formSchema>

/**
 * 登录页面组件
 */
export default function LoginPage() {
  const router = useRouter()
  const accessToken = useAtomValue(accessTokenAtom)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
        const signinMutation = useLogin()

  // 检查是否已登录
  useEffect(() => {
    if (accessToken) {

      router.push("/users")
    }
  }, [router,accessToken])

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  })

  // 处理登录
  const handleSubmit = (values: FormValues) => {
    setIsLoading(true)
    setError("")
    signinMutation.mutate(values, {
      onSuccess: () => {
        // 跳转到用户页面
        router.push("/users")
      },
      onError: (data) => {
        // setError(data)
        setIsLoading(false)
      },
    })
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">管理后台登录</CardTitle>
          <CardDescription>
            请输入您的用户名和密码登录系统
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-destructive/15 text-destructive rounded-md text-sm">
              {error}
            </div>
          )}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>用户名</FormLabel>
                    <FormControl>
                      <Input placeholder="输入用户名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>密码</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="输入密码" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    登录中...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <LogIn className="h-4 w-4" />
                    登录
                  </span>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
