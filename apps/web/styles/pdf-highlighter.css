/* PDF Highlighter 样式 */

.PdfHighlighter {
  height: 100%;
  width: 100%;
}

.PdfHighlighter__container {
  overflow: auto;
  height: 100%;
  position: relative;
}

.PdfHighlighter__page {
  position: relative;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.PdfHighlighter__page canvas {
  display: block;
  margin: 0 auto;
}

.PdfHighlighter__highlight {
  position: absolute;
  pointer-events: auto;
  cursor: pointer;
}

.PdfHighlighter__highlight--area {
  background: rgba(255, 226, 143, 0.5);
  border: 2px solid rgba(255, 193, 7, 0.8);
}

.PdfHighlighter__highlight--text {
  background: rgba(255, 255, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.6);
}

.PdfHighlighter__tip {
  position: absolute;
  z-index: 1000;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 8px;
  max-width: 300px;
  font-size: 14px;
  line-height: 1.4;
}

.PdfHighlighter__selection {
  position: absolute;
  background: rgba(0, 123, 255, 0.2);
  border: 1px solid rgba(0, 123, 255, 0.5);
  pointer-events: none;
}

/* 暗色主题适配 */
.dark .PdfHighlighter__tip {
  background: #1f2937;
  border-color: #374151;
  color: #f9fafb;
}

.dark .PdfHighlighter__page {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
