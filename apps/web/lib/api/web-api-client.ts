/**
 * Web应用专用的API客户端配置
 *
 * 基于UI库的fetch-client，为Web应用添加团队ID自动注入功能
 */

import { createApiClient, post as basePost, put as basePut, patch as basePatch } from '@ragtop-web/ui/lib/api'
import { getTeamId } from '@/store/team-store'
import type { FetchOptions } from '@ragtop-web/ui/lib/api'

/**
 * 需要排除team_id参数的路径
 */
const EXCLUDED_PATHS = ['/auth/signin', '/auth/signout', '/user/current']

/**
 * 不需要认证的路径
 */
const NO_AUTH_PATHS = ['/auth/signin']

/**
 * 检查路径是否需要添加team_id
 */
const shouldAddTeamId = (url: string): boolean => {
  return !EXCLUDED_PATHS.some(path => url.includes(path))
}

/**
 * 检查路径是否需要认证
 */
const shouldRequireAuth = (url: string): boolean => {
  return !NO_AUTH_PATHS.some(path => url.includes(path))
}

/**
 * Web应用专用的POST请求
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const post = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, pageNumber, pageSize, requireTeamId = true, ...restOptions } = options

  // 准备请求数据
  let finalData = data || {}

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10
    }
  }

  // 添加团队ID到请求体（只有在 requireTeamId 为 true 时）
  if (requireTeamId && shouldAddTeamId(url)) {
    const teamId = getTeamId()
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId
      }
    }
  }

  // 自动设置requireAuth
  const finalOptions = {
    ...restOptions,
    requireAuth: shouldRequireAuth(url),
    requireTeamId: requireTeamId && shouldAddTeamId(url)
  }
  return basePost<T>(url, finalData, finalOptions)
}

/**
 * Web应用专用的PUT请求
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const put = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, pageNumber, pageSize, ...restOptions } = options

  // 准备请求数据
  let finalData = data || {}

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10
    }
  }

  // 添加团队ID到请求体
  if (shouldAddTeamId(url)) {
    const teamId = getTeamId()
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId
      }
    }
  }

  // 自动设置requireAuth
  const finalOptions = {
    ...restOptions,
    requireAuth: shouldRequireAuth(url)
  }

  return basePut<T>(url, finalData, finalOptions)
}

/**
 * Web应用专用的PATCH请求
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const patch = <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
  const { isPaginated, pageNumber, pageSize, ...restOptions } = options

  // 准备请求数据
  let finalData = data || {}

  // 添加分页参数
  if (isPaginated) {
    finalData = {
      ...finalData,
      page_number: pageNumber || 1,
      page_size: pageSize || 10
    }
  }

  // 添加团队ID到请求体
  if (shouldAddTeamId(url)) {
    const teamId = getTeamId()
    if (teamId) {
      finalData = {
        ...finalData,
        team_id: teamId
      }
    }
  }

  // 自动设置requireAuth
  const finalOptions = {
    ...restOptions,
    requireAuth: shouldRequireAuth(url)
  }

  return basePatch<T>(url, finalData, finalOptions)
}

/**
 * 创建Web应用专用的API客户端
 * 自动为非排除路径添加team_id参数，自动设置requireAuth
 */
export const createWebApiClient = (baseConfig: Partial<FetchOptions> = {}) => {
  const baseClient = createApiClient(baseConfig)

  return {
    ...baseClient,
    post: <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
      return post<T>(url, data, { ...baseConfig, ...options })
    },
    put: <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
      return put<T>(url, data, { ...baseConfig, ...options })
    },
    patch: <T>(url: string, data?: any, options: FetchOptions = {}): Promise<T> => {
      return patch<T>(url, data, { ...baseConfig, ...options })
    }
  }
}

// 重新导出其他需要的函数和类型
export {
  get,
  del,
  fetchClient,
  getAccessToken,
  setAccessToken,
  clearAccessToken,
  setTeamId,
  clearTeamId,
  type FetchOptions,
  type ApiResponse,
  type PaginatedResponse,
  ApiError,
  API_SUCCESS_CODE,
  API_BAD_CREDENTIAL_CODE
} from '@ragtop-web/ui/lib/api'
