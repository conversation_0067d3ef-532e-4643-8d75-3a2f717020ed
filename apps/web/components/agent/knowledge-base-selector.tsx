"use client"

import { useState, useEffect, useRef } from "react"
import { Checkbox } from "@ragtop-web/ui/components/checkbox"
import { Label } from "@ragtop-web/ui/components/label"
import { ScrollArea } from "@ragtop-web/ui/components/scroll-area"
import { Loader2 } from "lucide-react"
import { createWebApiClient, PaginatedResponse } from "@/lib/api/web-api-client"

interface KnowledgeBase {
  id: string
  name: string
}

interface KnowledgeBaseSelectorProps {
  selectedIds: string[]
  onSelectionChange: (ids: string[]) => void
  maxHeight?: string
}

/**
 * 知识库选择器组件
 * 支持滚动加载和多选
 */
export function KnowledgeBaseSelector({
  selectedIds,
  onSelectionChange,
  maxHeight = "200px"
}: KnowledgeBaseSelectorProps) {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const pageSize = 10

  // 创建API客户端
  const apiClient = createWebApiClient({
    apiPrefix: "/api/v1/portal-ragtop/kbase",
  })

  // 获取知识库数据
  const fetchKnowledgeBases = async (page: number, reset = false) => {
    if (isLoading) return

    setIsLoading(true)
    try {
      const response = await apiClient.post<PaginatedResponse<KnowledgeBase>>('/query', {}, {
        isPaginated: true,
        pageNumber: page,
        pageSize
      })

      if (response?.records) {
        if (reset) {
          setKnowledgeBases(response.records)
        } else {
          setKnowledgeBases(prev => [...prev, ...response.records])
        }

        // 检查是否还有更多数据
        const totalLoaded = page * pageSize
        setHasMore(totalLoaded < (response.total || 0))
      }
    } catch (error) {
      console.error("获取知识库列表失败:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // 初始化加载
  useEffect(() => {
    fetchKnowledgeBases(1, true)
  }, [])

  // 滚动加载更多
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget

    // 当滚动到底部附近时加载更多
    if (scrollHeight - scrollTop <= clientHeight + 50 && hasMore && !isLoading) {
      const nextPage = currentPage + 1
      setCurrentPage(nextPage)
      fetchKnowledgeBases(nextPage)
    }
  }

  // 处理选择变化
  const handleSelectionChange = (knowledgeBaseId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedIds, knowledgeBaseId])
    } else {
      onSelectionChange(selectedIds.filter(id => id !== knowledgeBaseId))
    }
  }

  return (
    <ScrollArea
      ref={scrollAreaRef}
      className="border rounded-md p-3"
      style={{ height: maxHeight }}
      onScrollCapture={handleScroll}
    >
      <div className="space-y-3">
        {knowledgeBases.map((kb) => (
          <div key={kb.id} className="flex items-center space-x-2">
            <Checkbox
              id={`kb-${kb.id}`}
              checked={selectedIds.includes(kb.id)}
              onCheckedChange={(checked) => handleSelectionChange(kb.id, !!checked)}
            />
            <Label htmlFor={`kb-${kb.id}`} className="cursor-pointer text-sm">
              {kb.name}
            </Label>
          </div>
        ))}

        {/* 加载状态 */}
        {isLoading && (
          <div className="flex items-center justify-center py-2">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span className="text-sm text-muted-foreground">加载中...</span>
          </div>
        )}

        {/* 已加载完毕提示 */}
        {!hasMore && knowledgeBases.length > 0 && (
          <div className="text-center py-2">
            <span className="text-sm text-muted-foreground">已加载全部</span>
          </div>
        )}

        {/* 空状态 */}
        {!isLoading && knowledgeBases.length === 0 && (
          <div className="text-center py-4">
            <span className="text-sm text-muted-foreground">暂无知识库</span>
          </div>
        )}
      </div>
    </ScrollArea>
  )
}
