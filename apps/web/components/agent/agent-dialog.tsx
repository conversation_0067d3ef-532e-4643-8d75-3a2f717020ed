"use client"

import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { AgentCreateParams, AgentDetailsParams, Flexibility, Scope, Type, settledModelVariableMap } from "@/service/agent-service"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  RequiredFormLabel,
  FormMessage
} from "@ragtop-web/ui/components/form"
import { Input } from "@ragtop-web/ui/components/input"
import { Textarea } from "@ragtop-web/ui/components/textarea"
import { Button } from "@ragtop-web/ui/components/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@ragtop-web/ui/components/select"
import { Slider } from "@ragtop-web/ui/components/slider"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@ragtop-web/ui/components/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@ragtop-web/ui/components/tabs"
import { Label } from "@ragtop-web/ui/components/label"
import { Separator } from "@ragtop-web/ui/components/separator"
import { useExistingModels } from "@/service/model-service"
import { KnowledgeBaseSelector } from "./knowledge-base-selector"
import { DatasetSelector } from "./dataset-selector"


// 默认表单数据
const defaultFormData: AgentCreateParams = {
  name: "",
  description: "",
  abnormal_message: "",
  hello_message: "你好！我是你的助手，有什么可以帮到你的吗？",
  type: Type.Rag, // 默认资源类型为"知识库"
  link_resources: [], // 默认资源为空
  scope: Scope.Private, // 默认Agent权限为"个人"
  prompt: "你是一个专业的商务助手，只需回答商务的问题。",
  settings: {
    flexibility: Flexibility.Deliberate,
    keyword_weight: 0.5,
    llm_model: undefined,
    similarity_threshold: 0.5,
    temperature: 0.1,
    top_n: 5,
    top_p: 0.3,
  },
}

interface AgentDialogProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: AgentCreateParams) => void
  initialData?: AgentDetailsParams
}

/**
 * Agent 对话框组件，包含三个标签页：助理设置、提示引擎、模型设置
 */
export function AgentDialog({ open, onClose, onSubmit, initialData }: AgentDialogProps) {
  const { data: existingModels } = useExistingModels()
  const chatModels = existingModels?.filter(model => model.model_type === "CHAT") || []

  const defaultValue = initialData ? {
    ...initialData,
    id: undefined,
    link_resources: initialData?.link_resources?.map((resource: any) => resource.res_id),
    settings: {
      ...initialData.settings,
      llm_model: initialData?.settings?.llm_model,
      llm_model_name: undefined // 详情会有这个字段，传的时候不需要
    },
  } : defaultFormData

  const form = useForm<AgentCreateParams>({
    defaultValues: defaultValue
  })

  const resourceType = form.watch("type");
  const flexibility = form.watch("settings.flexibility");

  // 监听自由度变化，自动更新temperature和top_p
  useEffect(() => {
    if (flexibility && settledModelVariableMap[flexibility]) {
      const { temperature, top_p } = settledModelVariableMap[flexibility];
      form.setValue("settings.temperature", temperature);
      form.setValue("settings.top_p", top_p);
    }
  }, [flexibility, form]);

  // 处理表单提交
  const handleSubmit = (data: AgentCreateParams) => {
    const finalData = resourceType === Type.Rag ? {
      ...data,
     
    } : {
      ...data,
      settings:undefined
    }
    onSubmit(finalData)
    onClose()
  }


  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>聊天配置</DialogTitle>
          <p className="text-sm text-muted-foreground">在这里，为你的专业知识库装扮专属助手！</p>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="h-full flex flex-col">
              <div className="flex-1 overflow-y-auto space-y-4 pr-2">
                <FormField
              control={form.control}
              name="type"
              rules={{ required: "请选择资源类型" }}
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className="after:content-['*'] after:ml-0.5">资源类型</FormLabel>
                  <FormControl>
                    <div className="flex items-center space-x-6">
                      <div className="flex items-center space-x-2">
                        <input
                          disabled={initialData ? true : false}
                          type="radio"
                          id={Type.Rag}
                          value={Type.Rag}
                          checked={field.value === Type.Rag}
                          onChange={() => field.onChange(Type.Rag)}
                          className="h-4 w-4 text-primary border-input focus:ring-primary"
                        />
                        <Label htmlFor={Type.Rag} className="cursor-pointer">知识库</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          disabled={initialData ? true : false}
                          id={Type.NlToSQL}
                          value={Type.NlToSQL}
                          checked={field.value === Type.NlToSQL}
                          onChange={() => field.onChange(Type.NlToSQL)}
                          className="h-4 w-4 text-primary border-input focus:ring-primary"
                        />
                        <Label htmlFor={Type.NlToSQL} className="cursor-pointer">数据集</Label>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="link_resources"
              rules={{ required: "请选择资源" }}
              render={({ field }) => {
                if (resourceType === Type.Rag) {
                  // 知识库多选
                  return (
                    <FormItem>
                      <RequiredFormLabel>知识库</RequiredFormLabel>
                      <KnowledgeBaseSelector
                        selectedIds={field.value || []}
                        onSelectionChange={field.onChange}
                        maxHeight="200px"
                      />
                      <FormMessage />
                    </FormItem>
                  );
                } else {
                  // 数据集多选
                  return (
                    <FormItem>
                      <RequiredFormLabel>数据集</RequiredFormLabel>
                      <DatasetSelector
                        selectedIds={field.value || []}
                        onSelectionChange={field.onChange}
                        maxHeight="200px"
                      />
                      <FormMessage />
                    </FormItem>
                  );
                }
              }}
            />

            <Separator />
            <Tabs defaultValue="assistant-settings" className="w-full">
              <TabsList className={`w-full grid grid-cols-${resourceType === Type.Rag ? 3 : 2} mb-4`}>
                <TabsTrigger value="assistant-settings">基础设置</TabsTrigger>
                <TabsTrigger value="prompt-engine">提示引擎</TabsTrigger>
                {resourceType === Type.Rag && <TabsTrigger value="model-settings">模型设置</TabsTrigger>}
              </TabsList>

              {/* 助理设置标签页 */}
              <TabsContent value="assistant-settings" className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  rules={{ required: "Agent名称不能为空" }}
                  render={({ field }) => (
                    <FormItem>
                      <RequiredFormLabel>Agent名称</RequiredFormLabel>
                      <FormControl>
                        <Input placeholder="测试Agent" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Agent描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="你是一个专业的商务助手，只需回答商务的问题。"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="abnormal_message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>空回复</FormLabel>
                      <FormControl>
                        <Input placeholder="抱歉，我无法回答这个问题。" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hello_message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>设置开场白</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="你好！我是你的助手，有什么可以帮到你的吗？"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="scope"
                  rules={{ required: "请选择Agent权限" }}
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <RequiredFormLabel>Agent权限</RequiredFormLabel>
                      <FormControl>
                        <div className="flex items-center space-x-6">
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="PRIVATE"
                              value="PRIVATE"
                              checked={field.value === "PRIVATE"}
                              onChange={() => field.onChange("PRIVATE")}
                              className="h-4 w-4 text-primary border-input focus:ring-primary"
                            />
                            <Label htmlFor="PRIVATE" className="cursor-pointer">个人</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="radio"
                              id="TEAM_PUBLIC"
                              value="TEAM_PUBLIC"
                              checked={field.value === "TEAM_PUBLIC"}
                              onChange={() => field.onChange("TEAM_PUBLIC")}
                              className="h-4 w-4 text-primary border-input focus:ring-primary"
                            />
                            <Label htmlFor="TEAM_PUBLIC" className="cursor-pointer">团队</Label>
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />


              </TabsContent>

              {/* 提示引擎标签页 */}
              <TabsContent value="prompt-engine" className="space-y-4">
                <FormField
                  control={form.control}
                  name="prompt"
                  rules={{ required: "系统提示词不能为空" }}
                  render={({ field }) => (
                    <FormItem>
                      <RequiredFormLabel>系统提示词</RequiredFormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="你是一个学术领域的专家，请根据知识库的内容来尽可能详细的回答问题。"
                          className="min-h-32"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {resourceType === Type.Rag && <>
                  <FormField
                    control={form.control}
                    name="settings.similarity_threshold"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>相似度阈值</FormLabel>
                          <span className="text-sm">{field.value}</span>
                        </div>
                        <FormControl>
                          <Slider
                            value={[field.value || 0]}
                            min={0}
                            max={1}
                            step={0.01}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.keyword_weight"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>关键字相似度权重</FormLabel>
                          <span className="text-sm">{field.value}</span>
                        </div>
                        <FormControl>
                          <Slider
                            value={[field.value || 0]}
                            min={0}
                            max={1}
                            step={0.01}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.top_n"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center justify-between">
                          <FormLabel>Top N</FormLabel>
                          <span className="text-sm">{field.value}</span>
                        </div>
                        <FormControl>
                          <Slider
                            value={[field.value || 1]}
                            min={1}
                            max={20}
                            step={1}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {/* <FormField
                  control={form.control}
                  name="refineMultiturn"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <FormLabel>多轮对话优化</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                                <FormField
                  control={form.control}
                  name="reasoning"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <FormLabel>推理</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                /> */}
                </>}

              </TabsContent>

              {/* 模型设置标签页 */}
              <TabsContent value="model-settings" className="space-y-4">
                <FormField
                  control={form.control}
                  name="settings.llm_model"
                  rules={resourceType === Type.Rag ? { required: "请选择模型" } : undefined}
                  render={({ field }) => (
                    <FormItem>
                      {resourceType === Type.Rag ? <RequiredFormLabel>模型</RequiredFormLabel> : <FormLabel>模型</FormLabel>}
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="请选择模型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {chatModels.map((model) => (
                            <SelectItem key={model.llm_id} value={model.llm_id}>
                              {model.llm_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="settings.flexibility"
                  rules={{ required: "请选择自由度" }}
                  render={({ field }) => (
                    <FormItem>
                      <RequiredFormLabel>自由度</RequiredFormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="请选择自由度" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={Flexibility.Improvised}>即兴创作 (Improvised)</SelectItem>
                          <SelectItem value={Flexibility.Deliberate}>精确 (Deliberate)</SelectItem>
                          <SelectItem value={Flexibility.Balanced}>平衡 (Balanced)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="settings.temperature"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>温度</FormLabel>
                        <span className="text-sm">{field.value || 0}</span>
                      </div>
                      <FormControl>
                        <Slider
                          value={[field.value || 0]}
                          min={0}
                          max={1}
                          step={0.01}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="settings.top_p"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>Top P</FormLabel>
                        <span className="text-sm">{field.value || 0}</span>
                      </div>
                      <FormControl>
                        <Slider
                          value={[field.value || 0]}
                          min={0}
                          max={1}
                          step={0.01}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
            </Tabs>
              </div>

              <DialogFooter className="flex-shrink-0 mt-4">
                <Button type="button" variant="outline" onClick={onClose}>
                  取消
                </Button>
                <Button type="submit">
                  确定
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
