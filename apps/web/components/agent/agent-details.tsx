"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@ragtop-web/ui/components/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card"
import { Flexibility, Type, Scope, Agents,AgentDetailsParams } from "@/service/agent-service"

interface AgentDetailsProps {
  agent:  AgentDetailsParams
}

/**
 * Agent详情组件，展示Agent的所有信息，包括助理设置、提示引擎和模型设置
 */
export function AgentDetails({ agent}: AgentDetailsProps) {

  // 辅助函数：获取agent名称（兼容不同接口）
  const getAgentName = () => {
    return (agent as Agents).name || (agent as AgentDetailsParams).name || "未命名Agent"
  }

  // 辅助函数：获取agent资源列表（兼容不同接口）
  const getAgentResources = () => {
    return (agent as AgentDetailsParams).link_resources?.map(({res_name})=>res_name) || []
  }

  // 辅助函数：获取agent可见性/权限（兼容不同接口）
  const getAgentScope = () => {
    const scope = (agent as Agents).scope
    if (scope === "PRIVATE") return Scope.Private
    if (scope === "TEAM_PUBLIC") return Scope.TeamPublic
    return (agent as AgentDetailsParams).scope || Scope.Private
  }

  return (
    <div className="space-y-6">

      <Tabs defaultValue="assistant-settings" className="w-full">
        <TabsList className={`w-full grid ${agent.type === Type.Rag ? "grid-cols-3" : "grid-cols-2"} mb-4`}>
          <TabsTrigger value="assistant-settings">助理设置</TabsTrigger>
          <TabsTrigger value="prompt-engine">提示引擎</TabsTrigger>
          {agent.type === Type.Rag && <TabsTrigger value="model-settings">模型设置</TabsTrigger>}
        </TabsList>

        {/* 助理设置标签页 */}
        <TabsContent value="assistant-settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Agent姓名</h3>
                  <p>{getAgentName()}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">资源类型</h3>
                  <p>{agent.type === Type.Rag  ? "知识库" : "数据集"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">{agent.type === Type.Rag  ? "知识库" : "数据集"}</h3>
                  <p>{getAgentResources().join(", ") || "无"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Agent权限</h3>
                  <div className="flex items-center space-x-6 mt-1">
                    <div className="flex items-center space-x-2">
                      <div className={`h-4 w-4 rounded-full border ${getAgentScope() === Scope.Private ? "bg-primary border-primary" : "bg-background border-input"}`}>
                        {getAgentScope() === Scope.Private && (
                          <div className="h-2 w-2 mx-auto mt-0.5 rounded-full bg-white"></div>
                        )}
                      </div>
                      <span className={getAgentScope() === Scope.Private ? "font-medium" : ""}>个人</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`h-4 w-4 rounded-full border ${getAgentScope() === Scope.TeamPublic ? "bg-primary border-primary" : "bg-background border-input"}`}>
                        {getAgentScope() === Scope.TeamPublic && (
                          <div className="h-2 w-2 mx-auto mt-0.5 rounded-full bg-white"></div>
                        )}
                      </div>
                      <span className={getAgentScope() === Scope.TeamPublic ? "font-medium" : ""}>团队</span>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Agent描述</h3>
                <p>{agent.description || "无描述"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">开场白</h3>
                <p>{agent.hello_message}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">空回复</h3>
                <p>{agent.abnormal_message || "无设置"}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 提示引擎标签页 */}
        <TabsContent value="prompt-engine" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>提示词设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">系统提示词</h3>
                <pre className="p-4 bg-muted rounded-md whitespace-pre-wrap text-sm">
                  {agent.prompt}
                </pre>
              </div>
            </CardContent>
          </Card>

          {agent.type === Type.Rag && (
            <Card>
              <CardHeader>
                <CardTitle>检索设置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">相似度阈值</h3>
                    <p>{agent.settings?.similarity_threshold}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">关键词权重</h3>
                    <p>{agent.settings?.keyword_weight}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">检索数量</h3>
                    <p>{agent.settings?.top_n}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}


        </TabsContent>

        {/* 模型设置标签页 - 仅在知识库类型时显示 */}
        {agent.type === Type.Rag && (
          <TabsContent value="model-settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>模型配置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">模型</h3>
                    <p>{agent.settings?.llm_model_name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">温度</h3>
                    <p>{agent.settings?.temperature}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Top P</h3>
                    <p>{agent.settings?.top_p}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">自由度</h3>
                    <p>
                      {agent.settings?.flexibility === Flexibility.Improvised && "即兴创作 (Improvised)"}
                      {agent.settings?.flexibility === Flexibility.Deliberate && "精确 (Deliberate)"}
                      {agent.settings?.flexibility === Flexibility.Balanced && "平衡 (Balanced)"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
