"use client"

import { useEffect } from "react"
import { useCurrentUser, isAuthenticated } from "@/service/auth-service"

/**
 * 认证初始化组件
 * 
 * 在应用启动时检查用户登录状态，如果已登录则获取用户信息
 */
export function AuthInitializer({ children }: { children: React.ReactNode }) {
  const currentUserMutation = useCurrentUser()

  useEffect(() => {
    // 如果用户已登录，获取用户信息
    if (isAuthenticated()) {
      currentUserMutation.mutate(undefined, {
        onError: (error) => {
          console.error("初始化用户信息失败:", error)
          // 如果获取用户信息失败，可能是token过期，让fetch-client处理
        }
      })
    }
  }, [])

  return <>{children}</>
}
