"use client"

import { useCallback, useEffect, useRef, useState } from 'react'
import { IReferenceChunk } from '@/service/session-service'
import { useGetChunkHighlights } from '@/hooks/document-hooks'
import {
  PdfLoader, Highlight, PdfHighlighter,
  IHighlight, NewHighlight, Popup, AreaHighlight, Content,
  ScaledPosition,
} from 'react-pdf-highlighter'
import "pdfjs-dist/web/pdf_viewer.css";
import "react-pdf-highlighter/dist/style.css"; // 需要主动导入，不然会报错，坑踩的死死的

interface PdfPreviewerProps {
  url: string
  open: boolean
  chunk: IReferenceChunk
}

const HighlightPopup = ({
  comment,
}: {
  comment: { text: string; emoji: string };
}) =>
  comment.text ? (
    <div className="Highlight__popup">
      {comment.emoji} {comment.text}
    </div>
  ) : null;

/**
 * PDF 预览组件
 *
 * 基于抽屉形式展示 PDF 预览
 */
export function PdfPreviewer({
  url,
  open,
  chunk,
}: PdfPreviewerProps) {
  const { highlights: initialHighlights, setWidthAndHeight } = useGetChunkHighlights(chunk)
  const ref = useRef<(highlight: IHighlight) => void>(() => { });
  const [loaded, setLoaded] = useState(false);

  const [highlights, setHighlights] = useState<Array<IHighlight>>(initialHighlights);

  const addHighlight = useCallback((highlight: NewHighlight) => {
    // 这里可以添加新高亮的逻辑，目前只显示现有高亮
    console.log('Add highlight:', highlight)
  }, [])

  const resetHighlights = () => {
    setHighlights([]);
  };


  const updateHighlight = (
    highlightId: string,
    position: Partial<ScaledPosition>,
    content: Partial<Content>,
  ) => {
    console.log("Updating highlight", highlightId, position, content);
    setHighlights((prevHighlights) =>
      prevHighlights.map((h) => {
        const {
          id,
          position: originalPosition,
          content: originalContent,
          ...rest
        } = h;
        return id === highlightId
          ? {
            id,
            position: { ...originalPosition, ...position },
            content: { ...originalContent, ...content },
            ...rest,
          }
          : h;
      }),
    );
  };



  // 处理 PDF 文档加载完成
  const handlePdfDocumentLoad = useCallback((pdfDocument: any) => {
    if (pdfDocument) {
      pdfDocument.getPage(1).then((page: any) => {
        const viewport = page.getViewport({ scale: 1 })
        setWidthAndHeight(viewport.width, viewport.height)
      }).catch((error: any) => {
        console.error('Error loading PDF page:', error)
      })
    }
  }, [setWidthAndHeight])


  useEffect(() => {
    setLoaded(open);
  }, [open]);

  useEffect(() => {
    if (initialHighlights.length > 0 && loaded) {
      setLoaded(false);
      setHighlights(initialHighlights);
    }
  }, [initialHighlights, loaded]);

  return (
    <div className="flex flex-col h-full">
      {/* PDF 内容区域 */}
      <div className="flex-1 overflow-hidden bg-gray-100 dark:bg-gray-900">
        <div className="h-full relative">
          <PdfLoader
            url={url}
            // workerSrc="/pdfjs-dist/pdf.worker.min.js"
            beforeLoad={
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                  <p className="text-sm text-muted-foreground">加载中...</p>
                </div>
              </div>
            }>
            {(pdfDocument) => {
              // 当 PDF 文档加载完成时，处理页面尺寸设置
              handlePdfDocumentLoad(pdfDocument)

              return (
                  <PdfHighlighter
                    pdfDocument={pdfDocument}
                    enableAreaSelection={(event) => event.altKey}
                    onScrollChange={() => { }}
                    scrollRef={(scrollTo) => {
                      ref.current = scrollTo;
                      setLoaded(true);
                    }}
                    onSelectionFinished={() => null}
                  // onSelectionFinished={(
                  //   position,
                  //   content,
                  //   hideTipAndSelection
                  // ) => (
                  //   <div className="bg-white border border-gray-300 rounded shadow-lg p-2 max-w-xs">
                  //     <div className="text-sm text-gray-700 mb-2">
                  //       {content.text ? `${content.text.slice(0, 90)}...` : "选中的内容"}
                  //     </div>
                  //     <div className="flex gap-2">
                  //       <button
                  //         className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                  //         onClick={() => {
                  //           addHighlight({
                  //             content,
                  //             position,
                  //             comment: { text: "", emoji: "" }
                  //           })
                  //           hideTipAndSelection()
                  //         }}
                  //       >
                  //         高亮
                  //       </button>
                  //       <button
                  //         className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                  //         onClick={hideTipAndSelection}
                  //       >
                  //         取消
                  //       </button>
                  //     </div>
                  //   </div>
                  // )}
                  highlightTransform={(
                    highlight,
                    index,
                    setTip,
                    hideTip,
                    viewportToScaled,
                    screenshot,
                    isScrolledTo
                  ) => {
                    const isTextHighlight = !highlight.content?.image

                    const component = isTextHighlight ? (
                      <Highlight
                        isScrolledTo={isScrolledTo}
                        position={highlight.position}
                        comment={highlight.comment}
                      />
                    ) : (
                      <AreaHighlight
                        highlight={highlight}
                        isScrolledTo={isScrolledTo}
                        onChange={(boundingRect) => {
                          // updateHighlight(
                          //   highlight.id,
                          //   {
                          //     boundingRect: viewportToScaled(boundingRect)
                          //   },
                          //   {
                          //     image: screenshot(boundingRect)
                          //   }
                          // )
                        }}
                      />
                    )

                    return (
                      <Popup
                        popupContent={<HighlightPopup {...highlight} />}
                        onMouseOver={(popupContent) =>
                          setTip(highlight, () => popupContent)
                        }
                        onMouseOut={hideTip}
                        key={index}
                      >
                        {component}
                      </Popup>
                    )
                  }}
                    highlights={highlights}
                  />
              )
            }}
          </PdfLoader>
        </div>
      </div>
    </div>
  )
}
