"use client"

import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@ragtop-web/ui/components/select"
import { useExistingModels, useLLMSettings, useQueryLLMSettings } from "@/service/model-service"

// 表单验证模式
const formSchema = z.object({
  chat_model: z.string().min(1, "请选择适当的模型"),
  embedding_model: z.string().min(1, "请选择适当的模型"),
  rerank_model: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface PasswordChangeDialogProps {
  open: boolean
  onClose: () => void
}

/**
 * 密码修改对话框组件
 */
export function ModelChangeDialog({ open, onClose }: PasswordChangeDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState("")

  const { data: configuredModels } = useExistingModels(open)
  const { data: llmSettings } = useQueryLLMSettings(open)
  const settingMutate = useLLMSettings()

  const chatOption = configuredModels?.filter((option) => option.model_type === "CHAT")
  const embdOption = configuredModels?.filter((option) => option.model_type === "EMBEDDING")
  const rerankOption = configuredModels?.filter((option) => option.model_type === "RERANK")

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      chat_model: llmSettings?.chat_id || "",
      embedding_model: llmSettings?.embd_id || "",
      rerank_model: llmSettings?.rerank_id || "",
    },
  })

  // 处理密码修改
  const handleSubmit = (values: FormValues) => {
    settingMutate.mutate(values, {
      onSuccess: () => {
        onClose()
      }
    })
  }

  useEffect(()=>{
    if(llmSettings){
      form.reset({
        chat_model: llmSettings.chat_id,
        embedding_model: llmSettings.embd_id,
        rerank_model: llmSettings?.rerank_id,
      })
    }
  },[llmSettings])

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置默认模型</DialogTitle>
          <DialogDescription>
            请选择需要的模型
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="p-3 bg-destructive/15 text-destructive rounded-md text-sm">
            {error}
          </div>
        )}

        {success && (
          <div className="p-3 bg-green-500/15 text-green-500 rounded-md text-sm">
            密码修改成功！对话框将自动关闭...
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="chat_model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>聊天模型</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="请选择适当的模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {chatOption?.map((option) => (
                        <SelectItem
                          key={option.llm_id}
                          value={option.llm_id}>
                          {option.llm_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="embedding_model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>嵌入模型</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="请选择适当的模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {embdOption?.map((option) => (
                        <SelectItem
                          key={option.llm_id}
                          value={option.llm_id}>
                          {option.llm_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rerank_model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rerank模型</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="请选择适当的模型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {rerankOption?.map((option) => (
                        <SelectItem
                          key={option.llm_id}
                          value={option.llm_id}>
                          {option.llm_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                取消
              </Button>
              <Button type="submit" disabled={isLoading || success}>
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    提交中...
                  </span>
                ) : (
                  "确认"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
