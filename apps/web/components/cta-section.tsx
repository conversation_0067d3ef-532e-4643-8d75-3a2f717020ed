"use client"

import { But<PERSON> } from "@ragtop-web/ui/components/button"

/**
 * 行动召唤组件
 */
export function CTASection() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-primary text-primary-foreground">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center gap-4 text-center">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
            准备好开始了吗？
          </h2>
          <p className="max-w-[700px] md:text-xl">
            立即注册并开始使用我们的应用程序，体验现代化的 Web 开发。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 mt-6">
            <Button size="lg" variant="secondary">
              立即注册
            </Button>
            <Button variant="outline" size="lg" className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10">
              了解更多
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
