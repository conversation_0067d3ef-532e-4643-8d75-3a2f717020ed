# MarkdownRenderer 引用功能

## 功能概述

MarkdownRenderer 组件现在支持处理 `##数字$$` 格式的引用，并提供丰富的交互功能。

## 主要功能

### 1. 引用格式识别
- 自动识别 `##0$$`、`##1$$` 等格式
- 转换为可点击的数字按钮
- 按钮样式：圆形、主题色、悬停效果

### 2. Popover 显示
- **宽度**: 800px
- **最大高度**: 600px，超出时显示滚动条
- **布局**: 左右结构
  - 左侧：128x128 像素图片区域
  - 右侧：文字内容区域

### 3. 图片功能
- **显示**: 支持从 `image_uri` 字段加载图片
- **点击放大**: 点击图片在 Dialog 中查看大图
- **悬停效果**: 显示放大图标提示
- **错误处理**: 图片加载失败时显示占位符

### 4. PDF 预览
- **抽屉式预览**: 使用 Sheet 组件展示
- **工具栏功能**:
  - 缩放控制 (50% - 300%)
  - 旋转功能 (90度递增)
  - 下载功能
  - 页面导航 (如果支持)
- **响应式**: 支持不同屏幕尺寸

### 5. 文档链接处理
- **PDF 文件**: 点击文档名称打开抽屉预览
- **其他文件**: 在新标签页打开
- **安全性**: 使用 `noopener,noreferrer` 参数

## 使用方法

### 基本用法

```tsx
import { MarkdownRenderer } from '@/components/markdown-renderer'

const content = `
这是一个包含引用的文本 ##0$$，还有另一个引用 ##1$$。
`

const references = [
  {
    content: "引用内容文本",
    document_name: "文档.pdf",
    image_uri: "https://example.com/image.jpg",
    url: "https://example.com/document.pdf"
  }
]

<MarkdownRenderer 
  content={content}
  references={references}
  files={[]}
/>
```

### 引用数据结构

```typescript
interface Reference {
  content: string              // 引用的文本内容
  document_name: string        // 文档名称
  image_uri?: string          // 图片地址
  url?: string                // 文档链接
  document_id?: string        // 文档ID
}
```

## 组件结构

```
MarkdownRenderer
├── ReactMarkdown (主要内容渲染)
│   └── 自定义段落组件 (处理引用格式)
│       └── Popover (引用内容显示)
│           ├── 图片区域 (Dialog 放大)
│           └── 文字区域
└── PdfPreviewer (PDF 预览抽屉)
    ├── 工具栏
    ├── 页面导航
    └── PDF 内容区域
```

## 测试页面

访问 `/test-markdown` 查看完整的功能演示，包括：
- 不同类型的引用示例
- 图片点击放大效果
- PDF 预览功能
- 滚动和响应式布局

## 技术实现

### 核心依赖
- `react-markdown`: Markdown 渲染
- `@ragtop-web/ui`: UI 组件库
- `lucide-react`: 图标库

### 关键功能
1. **正则表达式**: `/##(\d+)\$\$/g` 识别引用格式
2. **状态管理**: React hooks 管理 PDF 预览状态
3. **事件处理**: 图片点击、文档链接点击
4. **样式系统**: Tailwind CSS 响应式设计

## 注意事项

1. **图片加载**: 确保图片 URL 可访问，支持 CORS
2. **PDF 链接**: PDF 文件需要支持在 iframe 中显示
3. **性能**: 大量引用时考虑虚拟化或分页
4. **安全性**: 外部链接使用安全参数打开

## 未来扩展

- [ ] 支持视频文件预览
- [ ] 添加引用内容搜索功能
- [ ] 支持引用内容编辑
- [ ] 添加引用统计和分析
- [ ] 支持引用内容导出
