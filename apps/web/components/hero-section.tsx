"use client"

import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { ArrowRight } from "lucide-react"

/**
 * 首页英雄区组件
 */
export function HeroSection() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-background">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center gap-4 text-center">
          <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
            欢迎使用 Ragtop 应用
          </h1>
          <p className="max-w-[700px] text-muted-foreground md:text-xl">
            一个现代化的 Web 应用程序，使用最新的技术栈构建，包括 Next.js、React 和 Tailwind CSS。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 mt-6">
            <Button size="lg">
              开始使用
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button variant="outline" size="lg">
              了解更多
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
