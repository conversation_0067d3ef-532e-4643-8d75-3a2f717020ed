"use client"

import * as React from "react"
import { useState } from "react"
import {
  UserRound,
  Bot,
  Database,
  BookOpenText,
  FileText,
  Plus,
} from "lucide-react"

import { NavMain } from "@/components/nav/nav-main"
import { NavProjects } from "@/components/nav/nav-projects"
import { NavUser } from "@/components/nav/nav-user"
import { TeamSwitcher } from "@/components/nav/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@ragtop-web/ui/components/sidebar"
import { NavSecondary } from "./nav/nav-secondary"
import { Button } from "@ragtop-web/ui/components/button"
import { AgentDialog } from "@/components/agent/agent-dialog"
import { useIsTeamAdmin, useCurrentUser } from "@/lib/user-role"
import { Scope, useAgents, useCreateAgent } from "@/service/agent-service"

// 导航数据
const getNavSecondary = (isAdmin: boolean) => {
  const items = []

  // 只有管理员才能看到模型配置
  if (isAdmin) {
    items.push({
      title: "模型配置",
      icon: Bo<PERSON>,
      url: "/models",
    })
  }

  return items
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [isAgentFormOpen, setIsAgentFormOpen] = useState(false)

  // 使用Hook获取状态
  const isAdmin = useIsTeamAdmin()
  const userData = useCurrentUser()
  const createAgent = useCreateAgent()
  const {data:privateAgents} = useAgents(Scope.Private)
  const {data:publicAgents} = useAgents(Scope.TeamPublic)

  const privateList = privateAgents?.records || []
  const publicList = publicAgents?.records || []

  // 处理添加 Agent
  const handleAddAgent = (data: any) => {
    createAgent.mutate(data)
  }

  const allProjects = [
      {
        name: "团队成员",
        url: "/users",
        icon: UserRound,
      },
      {
        name: "数据集",
        url: "/datasets",
        icon: Database,
      },
      {
        name: "知识库",
        url: "/knowledge-base",
        icon: BookOpenText,
      },
      {
        name: "文件",
        url: "/files",
        icon: FileText,
      },
    ]

  // 获取次要导航项（仅管理员可见）
  const getSecondaryNavItems = () => {
    return getNavSecondary(isAdmin)
  }

  return (
    <Sidebar collapsible="icon" variant="inset" {...props}>
      <SidebarHeader>
        <TeamSwitcher />
        <Button onClick={() => setIsAgentFormOpen(true)}>
          <Plus/>
          添加Agent
        </Button>
      </SidebarHeader>
      <SidebarContent>
       {isAdmin && <NavProjects title={"团队管理"} projects={allProjects} />}
        {getSecondaryNavItems().length > 0 && (
          <NavSecondary items={getSecondaryNavItems()} />
        )}
        <NavMain title={"个人Agent"} items={privateList} />
        <NavMain title={"团队Agent"} items={publicList} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser
          user={userData ? {
            name: userData.username || "用户",
            email: userData.username || "",
          } : {
            name: "用户",
            email: "",
          }}
        />
      </SidebarFooter>
      <SidebarRail />

      {/* Agent 添加对话框 */}
      <AgentDialog
        open={isAgentFormOpen}
        onClose={() => setIsAgentFormOpen(false)}
        onSubmit={handleAddAgent}
      />
    </Sidebar>
  )
}
