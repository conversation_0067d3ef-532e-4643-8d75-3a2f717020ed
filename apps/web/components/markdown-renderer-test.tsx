"use client"

import { MarkdownRenderer } from './markdown-renderer'

/**
 * 测试 MarkdownRenderer 组件的引用功能
 */
export function MarkdownRendererTest() {
  // 模拟包含引用的内容
  const testContent = `
根据您的问题，我为您找到了相关信息。

关于产品的基本功能，您可以参考 ##0$$，其中详细说明了产品的核心特性和使用方法。

对于高级配置选项，建议查看 ##1$$，这份文档包含了所有配置参数的详细说明。

## 技术规范

产品的技术规范如下：

- **性能指标**：参见 ##2$$
- **兼容性要求**：详见 ##3$$

\`\`\`bash
# 安装命令示例
npm install @example/product
\`\`\`

希望这些信息对您有帮助！如果您需要更多详细信息，请点击上方的引用链接查看完整文档。
  `;

  // 模拟引用数据
  const testReferences = [
    {
      content: "这是第一个引用的内容，来自某个PDF文档。这段内容比较长，用来测试文字换行的效果。内容包含了详细的说明和解释。\n\n这里是第二段内容，用来测试多段落的显示效果。内容包括：\n1. 产品功能介绍\n2. 使用方法说明\n3. 注意事项提醒\n4. 常见问题解答\n\n这是一个很长的引用内容，用来测试 Popover 的滚动功能。当内容超过最大高度时，应该出现滚动条。",
      document_name: "产品说明书.pdf",
      document_id: "doc1",
      image_uri: "https://via.placeholder.com/300x200/4f46e5/ffffff?text=PDF+Document",
      url: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
    },
    {
      content: "这是第二个引用的内容，包含更多详细信息。这个引用来自Word文档，没有图片但有链接。\n\n内容包括详细的配置说明：\n- 系统要求\n- 安装步骤\n- 配置参数\n- 故障排除\n\n这个引用测试没有图片的情况下的布局效果。",
      document_name: "用户手册.docx",
      document_id: "doc2",
      url: "https://example.com/doc2.docx"
    },
    {
      content: "第三个引用的内容，来自另一个来源。这个引用有图片但没有外部链接。\n\n这里测试有图片但没有链接的情况：\n• 图片可以点击放大\n• 文档名称不可点击\n• 内容支持换行显示\n\n这是一个中等长度的引用内容示例。",
      document_name: "内部文档.txt",
      document_id: "doc3",
      image_uri: "https://via.placeholder.com/300x200/10b981/ffffff?text=Internal+Doc"
    },
    {
      content: "最后一个引用的内容，这是一个PDF文档的引用，包含图片和链接。用来测试完整的功能。\n\n这个引用包含所有功能：\n✓ 图片显示和点击放大\n✓ PDF 文档预览\n✓ 长文本滚动\n✓ 多段落格式\n\n点击文档名称可以在抽屉中预览 PDF 内容，这是一个完整的功能演示。内容会根据实际需要自动换行和滚动。",
      document_name: "技术规范.pdf",
      document_id: "doc4",
      image_uri: "https://via.placeholder.com/300x200/f59e0b/ffffff?text=Tech+Spec",
      url: "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
    }
  ];

  const testFiles: any[] = [];

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">MarkdownRenderer 引用功能测试</h1>

      {/* 聊天消息示例 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">聊天消息示例：</h2>
        <div className="border rounded-lg p-4 bg-card">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium">
              AI
            </div>
            <div className="flex-1">
              <div className="bg-muted p-3 rounded-lg">
                <MarkdownRenderer
                  content={testContent}
                  references={testReferences}
                  files={testFiles}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 原始测试内容 */}
      <div className="border rounded-lg p-4 bg-card">
        <h2 className="text-lg font-semibold mb-2">原始 Markdown 内容：</h2>
        <pre className="text-sm text-muted-foreground bg-muted p-3 rounded overflow-x-auto whitespace-pre-wrap">
          {testContent}
        </pre>
      </div>

      <div className="mt-6">
        <h2 className="text-lg font-semibold mb-2">功能说明：</h2>
        <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
          <li>点击数字按钮可以查看引用内容</li>
          <li>引用格式为 ##数字$$，会自动转换为可点击按钮</li>
          <li>Popover 宽度为 800px，最大高度 600px，支持滚动</li>
          <li>采用左右布局：左侧显示图片（128x128），右侧显示文字内容</li>
          <li>图片支持点击放大，悬停时显示放大图标</li>
          <li>文字内容支持换行和多段落显示</li>
          <li>PDF 文件点击文档名称可在抽屉中预览，其他文件打开新页面</li>
          <li>如果没有图片或链接，对应部分会自动隐藏</li>
          <li>PDF 预览支持缩放、旋转、下载等功能</li>
        </ul>
      </div>
    </div>
  );
}
