# PDF 预览组件 (PdfPreviewer)

## 概述

`PdfPreviewer` 组件已经从原来的 iframe 实现升级为使用 `react-pdf-highlighter` 的真正 PDF 高亮预览组件。该组件支持基于 chunk 数据的自动高亮显示，以及用户交互式高亮功能。

## 主要特性

### ✅ 已实现功能

1. **真正的 PDF 渲染**：使用 `react-pdf-highlighter` 替代 iframe
2. **自动高亮显示**：基于 `chunk.positions` 数据自动生成高亮标记
3. **交互式高亮**：用户可以选择文本或区域进行高亮
4. **基本操作**：缩放、旋转、下载等功能
5. **响应式设计**：适配不同屏幕尺寸
6. **暗色主题支持**：自动适配系统主题

### 🔧 技术实现

- **PDF 渲染**：`react-pdf-highlighter` + `pdfjs-dist`
- **高亮数据处理**：`useGetChunkHighlights` hook
- **样式系统**：Tailwind CSS + 自定义 CSS
- **状态管理**：React hooks

## 使用方法

### 基本用法

```tsx
import { PdfPreviewer } from '@/components/pdf-previewer'

const MyComponent = () => {
  const [isOpen, setIsOpen] = useState(false)
  
  const chunk: IReferenceChunk = {
    id: "chunk-1",
    content: null,
    document_id: "doc-1",
    document_name: "文档.pdf",
    dataset_id: "dataset-1",
    image_id: "image-1",
    similarity: 0.95,
    vector_similarity: 0.92,
    term_similarity: 0.88,
    positions: [
      [1, 100, 300, 150, 180], // [页码, x1, x2, y1, y2]
      [1, 120, 350, 200, 230],
    ]
  }

  return (
    <PdfPreviewer
      url="https://example.com/document.pdf"
      fileName="示例文档.pdf"
      chunk={chunk}
      open={isOpen}
      onOpenChange={setIsOpen}
      trigger={<Button>打开 PDF</Button>}
    />
  )
}
```

### Props 说明

| 属性 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `url` | `string` | ✅ | PDF 文件的 URL |
| `fileName` | `string` | ✅ | 文件名称 |
| `chunk` | `IReferenceChunk` | ✅ | 包含高亮位置信息的 chunk 数据 |
| `open` | `boolean` | ❌ | 控制抽屉是否打开 |
| `onOpenChange` | `(open: boolean) => void` | ❌ | 抽屉状态变化回调 |
| `trigger` | `React.ReactNode` | ❌ | 触发打开的元素 |

### 高亮数据格式

`chunk.positions` 数组中每个元素的格式：
```typescript
[pageNumber, x1, x2, y1, y2]
```

- `pageNumber`: 页码（从 1 开始）
- `x1, y1`: 高亮区域左上角坐标
- `x2, y2`: 高亮区域右下角坐标

## 用户交互

### 基本操作

- **缩放**：使用工具栏的 + / - 按钮
- **旋转**：点击旋转按钮
- **下载**：点击下载按钮
- **页面导航**：使用左右箭头按钮

### 高亮功能

- **查看高亮**：点击黄色高亮区域查看详细信息
- **文本选择**：直接拖拽选择文本
- **区域选择**：按住 Alt 键拖拽选择矩形区域
- **添加高亮**：选择后点击"高亮"按钮

## 样式定制

组件使用了自定义 CSS 文件 `@/styles/pdf-highlighter.css`，可以根据需要修改样式：

```css
/* 高亮区域样式 */
.PdfHighlighter__highlight--text {
  background: rgba(255, 255, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.6);
}

/* 暗色主题适配 */
.dark .PdfHighlighter__tip {
  background: #1f2937;
  border-color: #374151;
  color: #f9fafb;
}
```

## 测试

访问 `/test-pdf` 页面可以测试 PDF 预览组件的功能。

## 注意事项

1. **PDF URL**：确保 PDF 文件 URL 可以正常访问
2. **CORS**：如果 PDF 文件在不同域名，需要配置 CORS
3. **文件大小**：大文件可能需要较长加载时间
4. **浏览器兼容性**：现代浏览器支持良好

## 未来改进

- [ ] 支持 PDF 文件上传预览
- [ ] 添加搜索功能
- [ ] 支持批注和评论
- [ ] 优化大文件加载性能
- [ ] 添加键盘快捷键支持
