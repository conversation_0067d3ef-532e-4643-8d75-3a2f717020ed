"use client"

import Link from "next/link"
import { Home, Info, Mail, Database } from "lucide-react"
import { ThemeToggle } from "@ragtop-web/ui/components/theme-toggle"
import { Button } from "@ragtop-web/ui/components/button"

/**
 * 导航栏组件
 *
 * 包含应用标题、导航链接和主题切换按钮
 */
export function Navbar() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-6">
          <Link href="/" className="flex items-center gap-2 font-semibold">
            <span className="text-xl">Ragtop</span>
          </Link>
          <nav className="hidden md:flex gap-6">
            <Link
              href="/"
              className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <Home className="h-4 w-4" />
              <span>首页</span>
            </Link>
            <Link
              href="/about"
              className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <Info className="h-4 w-4" />
              <span>关于</span>
            </Link>
            <Link
              href="/contact"
              className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <Mail className="h-4 w-4" />
              <span>联系我们</span>
            </Link>
            <Link
              href="/datasets"
              className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <Database className="h-4 w-4" />
              <span>数据集</span>
            </Link>
          </nav>
        </div>
        <div className="flex items-center gap-4">
          <div className="hidden md:flex">
            <Button variant="default" size="sm">
              登录
            </Button>
          </div>
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}
