"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import { Provider as <PERSON><PERSON>Provider } from "jotai"
import { QueryProvider } from "@ragtop-web/ui/components/query-provider"

/**
 * 应用程序提供者组件
 *
 * 包含主题提供者、状态管理提供者和查询提供者
 */
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <JotaiProvider>
      <QueryProvider>
        <NextThemesProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
          enableColorScheme
        >
          {children}
        </NextThemesProvider>
      </QueryProvider>
    </JotaiProvider>
  )
}
