"use client"

import { useCallback, useEffect, useRef, useState } from 'react'
import '@/styles/pdf-highlighter.css'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@ragtop-web/ui/components/sheet'
import { IReferenceChunk } from '@/service/session-service'
import { useGetChunkHighlights } from '@/hooks/document-hooks'
import {
  PdfLoader, Highlight,PdfHighlighter,
  IHighlight, NewHighlight, Popup, AreaHighlight
} from 'react-pdf-highlighter'
import { PdfPreviewer } from './pdf-previewer'

// import PdfHighlighter from './custom-pdf-high-lighter'

interface PdfPreviewerProps {
  url: string
  fileName: string
  trigger?: React.ReactNode
  open?: boolean
  chunk: IReferenceChunk
  onOpenChange?: (open: boolean) => void
}
/**
 * PDF 预览组件
 *
 * 基于抽屉形式展示 PDF 预览
 */
export function PdfDrawer({
  url,
  fileName,
  trigger,
  open,
  chunk,
  onOpenChange
}: PdfPreviewerProps) {

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        {trigger}
      </SheetTrigger>
      <SheetContent side="right" className="w-full sm:max-w-4xl p-0">
        <SheetHeader className="px-6 py-4 border-b">
          <SheetTitle className="text-lg font-semibold">{fileName}</SheetTitle>
          <SheetDescription className="text-sm text-muted-foreground">
            PDF 文档预览
          </SheetDescription>

        </SheetHeader>
        <PdfPreviewer chunk={chunk} url={url} open={open} />
      </SheetContent>
    </Sheet>
  )
}
