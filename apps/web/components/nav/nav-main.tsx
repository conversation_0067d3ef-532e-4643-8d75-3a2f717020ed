"use client"

import { useState } from "react"
import { ChevronRight, SquarePen, MoreHorizontal, Trash2, MessageSquarePlus, BotMessageSquare } from "lucide-react"
import { useRouter } from "next/navigation"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@ragtop-web/ui/components/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from "@ragtop-web/ui/components/sidebar"
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@ragtop-web/ui/components/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@ragtop-web/ui/components/alert-dialog"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { AgentDialog } from "@/components/agent/agent-dialog"
import {
  Flexibility,
  Type,
  Scope,
  AgentCreateParams,
  Agents,
  useUpdateAgent,
  useDeleteAgent
} from "@/service/agent-service"
import {
  useCreateSession
} from "@/service/session-service"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import { SessionList } from "./session-list"
import { useAtomValue } from "jotai"
import { currentTeamAtom } from "@/store/team-store"



export function NavMain({
  title,
  items,
}: {
  title: string
  items: {
    name: string
    id: string
  }[]
}) {
  const { isMobile } = useSidebar()
  const router = useRouter()
  const pathname = usePathname()
  const { toast } = useToast()
  const currentTeam = useAtomValue(currentTeamAtom)
  const [isAgentEditDialogOpen, setIsAgentEditDialogOpen] = useState(false)
  const [currentAgent, setCurrentAgent] = useState<Agents | null>(null)
  const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set())

  // API hooks
  const updateAgent = useUpdateAgent(currentAgent?.id || "")
  const deleteAgent = useDeleteAgent()
  const createSession = useCreateSession()

  // 切换Agent展开状态
  const toggleAgentExpanded = (agentId: string) => {
    const newExpanded = new Set(expandedAgents)
    if (newExpanded.has(agentId)) {
      newExpanded.delete(agentId)
    } else {
      newExpanded.add(agentId)
    }
    setExpandedAgents(newExpanded)
  }

  // 检查Agent是否被选中
  const isAgentSelected = (agentId: string) => {
    return pathname.startsWith(`/agent/${agentId}`)
  }

  // 检查用户是否可以删除Agent
  const canHandleAgent = (agent: Agents) => {
    if (!currentTeam) return false
    // 如果是agent的owner，可以删除
    if (agent.owner_id === currentTeam.member_id) return true
    // 其他情况不能删除
    return false
  }

  // 获取删除确认提示文本
  const getDeleteConfirmText = (agent: Agents) => {
    // 检查是否为团队Agent（通过visibility字段判断）
    const isTeamAgent = (agent as any).visibility === "TEAM_PUBLIC"
    if (isTeamAgent) {
      return "团队agent删除后，其余使用这个agent的用户的相关会话和数据也会被永久删除。"
    }
    return "您确定要删除这个Agent吗？此操作无法撤销，所有相关的会话和数据都将被永久删除。"
  }

  // 新增session
  const handleAddSession = async (agentId: string) => {
    try {
      const sessionData = {
        agent_id: agentId,
        title: `新会话`
      }

      const newSession = await createSession.mutateAsync(sessionData)
      toast({
        title: "成功",
        description: "会话创建成功",
      })

      // 导航到新会话
      router.push(`/agent/${agentId}/${newSession}`)
    } catch (error) {
      console.error("创建会话失败:", error)
      toast({
        title: "错误",
        description: "创建会话失败",
        variant: "destructive",
      })
    }
  }

  // 处理编辑Agent
  const handleEditAgent = (agent: Agents) => {
    setCurrentAgent(agent)
    setIsAgentEditDialogOpen(true)
  }

  // 处理更新Agent
  const handleUpdateAgent = async (data: AgentCreateParams) => {
    if (!currentAgent) return

    try {
      await updateAgent.mutateAsync({ ...data, agent_id: currentAgent.id })
      toast({
        title: "成功",
        description: "Agent更新成功",
      })
      setIsAgentEditDialogOpen(false)
    } catch (error) {
      console.error("更新Agent失败:", error)
      toast({
        title: "错误",
        description: "更新Agent失败",
        variant: "destructive",
      })
    }
  }

  // 处理删除Agent
  const handleDeleteAgent = async (agentId: string) => {
    try {
      await deleteAgent.mutateAsync({ agent_id: agentId })
      toast({
        title: "成功",
        description: "Agent删除成功",
      })
    } catch (error) {
      console.error("删除Agent失败:", error)
      toast({
        title: "错误",
        description: "删除Agent失败",
        variant: "destructive",
      })
    }
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarMenu>
        {items?.map((item) => (
          <Collapsible
            key={item.id}
            asChild
            className="group/collapsible"
          >
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                isActive={isAgentSelected(item.id)}
              >
                <Link href={`/agent/${item.id}`}>
                  <BotMessageSquare />
                  <span>{item.name}</span>
                </Link>
              </SidebarMenuButton>
              <CollapsibleTrigger asChild>
                <SidebarMenuAction
                  className="left-2 bg-sidebar-accent text-sidebar-accent-foreground data-[state=open]:rotate-90"
                  showOnHover
                  onClick={() => toggleAgentExpanded(item.id)}
                >
                  <ChevronRight />
                </SidebarMenuAction>
              </CollapsibleTrigger>
              <SidebarMenuAction showOnHover onClick={() => handleAddSession(item.id)}>
                <MessageSquarePlus className="h-4 w-4 mr-10" />
              </SidebarMenuAction>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuAction showOnHover>
                    <MoreHorizontal />
                    <span className="sr-only">More</span>
                  </SidebarMenuAction>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-48 rounded-lg"
                  side={isMobile ? "bottom" : "right"}
                  align={isMobile ? "end" : "start"}
                >
                  <DropdownMenuItem onClick={() => handleEditAgent(item as Agents)} disabled={!canHandleAgent(item as Agents)}>
                    <SquarePen className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>编辑Agent</span>
                  </DropdownMenuItem>

                  {canHandleAgent(item as Agents) ? (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                          <Trash2 className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>删除Agent</span>
                        </DropdownMenuItem>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>确认删除</AlertDialogTitle>
                          <AlertDialogDescription>
                            {getDeleteConfirmText(item as Agents)}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>取消</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleDeleteAgent(item.id)}>
                            确认删除
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  ) : (
                    <DropdownMenuItem disabled>
                      <Trash2 className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>删除Agent</span>
                    </DropdownMenuItem>
                  )}

                </DropdownMenuContent>
              </DropdownMenu>
              <CollapsibleContent>
                <SessionList
                  agentId={item.id}
                  isExpanded={expandedAgents.has(item.id)}
                />
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>

      {/* Agent 编辑对话框 */}
      {currentAgent && (
        <AgentDialog
          open={isAgentEditDialogOpen}
          onClose={() => setIsAgentEditDialogOpen(false)}
          onSubmit={handleUpdateAgent}
          initialData={{
            name: (currentAgent as any).title || (currentAgent as any).name || "",
            description: "",
            abnormal_message: "",
            hello_message: "你好！我是你的助手，有什么可以帮到你的吗？",
            type: currentAgent.type as Type,
            link_resources: (currentAgent as any).resource_ids || [],
            scope: (currentAgent as any).visibility === "PRIVATE" ? Scope.Private : Scope.TeamPublic,
            prompt: (currentAgent as any).settings?.agent_prompt || "",
            settings: {
              flexibility: Flexibility.Deliberate,
              keyword_weight: (currentAgent as any).settings?.keyword_weight || 0.5,
              llm_model: (currentAgent as any).settings?.llm_model,
              similarity_threshold: (currentAgent as any).settings?.similarity_threshold || 0.5,
              temperature: (currentAgent as any).settings?.temperature || 0.1,
              top_n: (currentAgent as any).settings?.top_n || 5,
              top_p: (currentAgent as any).settings?.top_p || 0.3,
            },
          }}
        />
      )}
    </SidebarGroup>
  )
}

