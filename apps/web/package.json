{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@radix-ui/react-separator": "1.1.6", "@radix-ui/react-slot": "^1.1.2", "@ragtop-web/ui": "workspace:*", "@tanstack/react-query": "5.76.1", "class-variance-authority": "^0.7.1", "highlight.js": "11.11.1", "jotai": "^2.7.0", "lodash-es": "4.17.21", "lucide-react": "^0.475.0", "next": "^15.3.3", "next-themes": "^0.4.4", "pdfjs-dist": "4.4.168", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.51.0", "react-markdown": "10.1.0", "react-pdf-highlighter": "8.0.0-rc.0", "react-syntax-highlighter": "15.6.1", "rehype-highlight": "7.0.2", "rehype-katex": "7.0.1", "rehype-raw": "7.0.0", "remark-gfm": "4.0.1", "remark-math": "6.0.0", "sonner": "2.0.3", "uuid": "11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@ragtop-web/eslint-config": "workspace:^", "@ragtop-web/typescript-config": "workspace:*", "@types/lodash-es": "4.17.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "15.5.13", "@types/uuid": "10.0.0", "typescript": "^5.7.3"}}