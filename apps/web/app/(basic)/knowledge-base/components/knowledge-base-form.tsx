"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ragtop-web/ui/components/select"
import { SliceMethodType, type KnowledgeBaseParams } from "@/service"
import { SliceMethodDescription, sliceMethodOptions } from "./slice-method-description"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(1, "知识库名称不能为空"),
  parser_provider_id: z.literal("DeepDOC"),
  chunk_provider_id: z.string().min(1, "切片方法不能为空"),
})

type FormValues = z.infer<typeof formSchema>

interface KnowledgeBaseFormProps {
  knowledgeBase?: KnowledgeBaseParams
  onSave: (knowledgeBase: KnowledgeBaseParams) => void
  isCreating: boolean
  isLoading: boolean

}

export function KnowledgeBaseForm({ knowledgeBase, onSave, isCreating, isLoading }: KnowledgeBaseFormProps) {
  const [selectedMethod, setSelectedMethod] = useState<SliceMethodType>(
    (knowledgeBase?.chunk_provider_id as SliceMethodType) || "naive"
  )

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: knowledgeBase
      ? {
        ...knowledgeBase,
        parser_provider_id: "DeepDOC" // 确保始终使用 DeepDOC
      }
      : {
        name: "",
        parser_provider_id: "DeepDOC",
        chunk_provider_id: "naive",
      },
  })

  // 监听切片方法变化
  const watchSliceMethod = form.watch("chunk_provider_id") as SliceMethodType

  useEffect(() => {
    if (watchSliceMethod) {
      setSelectedMethod(watchSliceMethod)
    }
  }, [watchSliceMethod])


  // 保存知识库
  const handleSubmit = (values: FormValues) => {
    onSave({
      id: knowledgeBase?.id || undefined,
      name: values.name,
      chunk_config: {
        chunk_provider_id : values.chunk_provider_id as SliceMethodType
      },
      pdf_parser_config: {
        parser_provider_id: "DeepDOC"
      },
      files: knowledgeBase?.files || undefined,
    })
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">

        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>知识库名称</FormLabel>
              <FormControl>
                <Input placeholder="输入知识库名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="parser_provider_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>PDF解析器</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue="DeepDOC"
                disabled
              >
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择PDF解析器">DeepDOC</SelectValue>
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="DeepDOC">DeepDOC</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                使用 DeepDOC 解析 PDF 文档
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="chunk_provider_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>切片方法</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择切片方法" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {sliceMethodOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                选择文档切片的方法
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 切片方法说明 */}
        <div className="border rounded-md p-3 bg-muted/30">
          <h4 className="font-medium mb-2">方法说明</h4>
          <SliceMethodDescription method={selectedMethod} />
        </div>
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isLoading}>
            {isLoading && <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />}
            {isCreating ? "创建知识库" : "保存修改"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
