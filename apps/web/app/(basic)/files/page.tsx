"use client"

import { useState} from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@ragtop-web/ui/components/dialog"
import { FileList } from "./components/file-list"
import { FileUpload } from "./components/file-upload"
import { AddFileDropdown } from "./components/add-file-dropdown"
import { RenameFileDialog } from "./components/rename-file-dialog"

import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import {
  useFiles,
  useDeleteFile,
  useRenameFile,
  type FileItem,
} from "@/service"

/**
 * 文件管理页面
 */
// 2025.5.16: 本期先不做文件夹的支持，暂时隐藏面包屑，新增文件按钮改成单独的上传文件
export default function FilesPage() {
  const { toast } = useToast()
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false)
  const [pageNumber, setPageNumber] = useState(1)
  const [pageSize] = useState(20)

  // 获取文件列表
  const { data: filesData, isLoading, error } = useFiles(pageNumber, pageSize)
  const files = filesData?.records || []
  // 获取文件夹内容
  // const folderContents = useFolderContents()

  // 文件操作hooks
  // const createFolder = useCreateFolder()
  const deleteFile = useDeleteFile()
  const renameFile = useRenameFile()
  // const moveFile = useMoveFile()
  // const fileReferences = useFileReferences()

  // 获取当前目录下的文件和文件夹
  // const currentFiles = files.filter(file => file.parentId === currentParentId)

    const currentFiles = files.filter(file => file.type !== 'folder')

  // 获取所有文件夹（用于移动文件时选择）
  // const allFolders = files.filter(file => file.isFolder)

  // 处理文件上传完成
  // const handleUploadComplete = (uploadedFiles: FileUploadItem[]) => {
    // const successFiles = uploadedFiles.filter(f => f.status === 'success')

    // if (successFiles.length > 0) {
    //   // 使用批量上传API
    //   const files = successFiles.map(f => f.file)
    //   uploadFiles.mutate(files, {
    //     onSuccess: (results) => {
    //       toast({
    //         title: "上传成功",
    //         description: `成功上传 ${results.length} 个文件`,
    //       })
    //       setIsUploadDialogOpen(false)
    //     },
    //     onError: (error) => {
    //       toast({
    //         title: "上传失败",
    //         description: "文件上传过程中发生错误",
    //         variant: "destructive",
    //       })
    //     }
    //   })
    // }
  // }

  // 处理创建文件夹
  // const handleCreateFolder = (folderName: string) => {
  //   createFolder.mutate(
  //     {
  //       name: folderName,
  //       parentId: currentParentId
  //     },
  //     {
  //       onSuccess: (newFolder) => {
  //         toast({
  //           title: "创建成功",
  //           description: `文件夹 ${newFolder.name} 已创建`,
  //         })
  //         setIsCreateFolderDialogOpen(false)
  //       },
  //       onError: (error) => {
  //         toast({
  //           title: "创建失败",
  //           description: "创建文件夹时发生错误",
  //           variant: "destructive",
  //         })
  //       }
  //     }
  //   )
  // }

  // 处理文件删除
  const handleDeleteFile = (fileId: string) => {
    deleteFile.mutate(
      { file_id: fileId },
      {
        onSuccess: () => {
          toast({
            title: "删除成功",
            description: "文件已成功删除",
          })
        },
        onError: (error) => {
          toast({
            title: "删除失败",
            description: "删除文件时发生错误",
            variant: "destructive",
          })
        }
      }
    )
  }

  // 处理文件夹点击
  // const handleFolderClick = (folderId: string) => {
  //   const folder = files.find(f => f.id === folderId)
  //   if (folder) {
  //     setCurrentParentId(folderId)
  //     setCurrentPath(folder.path + "/")
  //   }
  // }

  // // 处理导航
  // const handleNavigate = (path: string) => {
  //   if (path === "/") {
  //     // 返回根目录
  //     setCurrentParentId(null)
  //     setCurrentPath("/")
  //   } else {
  //     // 查找对应的文件夹
  //     const targetPath = path.endsWith("/") ? path : path + "/"
  //     const folder = files.find(f => f.path === path && f.isFolder)

  //     if (folder) {
  //       setCurrentParentId(folder.id)
  //       setCurrentPath(targetPath)
  //     }
  //   }
  // }

  // 处理链接知识库
  // const handleLinkKnowledgeBase = (file: FileItem) => {
  //   setSelectedFile(file)
  //   setIsLinkDialogOpen(true)
  // }

  // 处理确认链接知识库
  // const handleConfirmLink = (knowledgeBaseIds: string[]) => {
  //   if (!selectedFile) return

  //   // 这里应该调用API将文件链接到知识库
  //   // 目前API接口尚未提供，暂时使用本地状态模拟
  //   toast({
  //     title: "链接成功",
  //     description: `文件已链接到 ${knowledgeBaseIds.length} 个知识库`,
  //   })

  //   setIsLinkDialogOpen(false)
  // }

  // 处理重命名
  const handleRenameFile = (file: FileItem) => {
    setSelectedFile(file)
    setIsRenameDialogOpen(true)
  }

  // 处理确认重命名
  const handleConfirmRename = (newName: string) => {
    if (!selectedFile) return

    renameFile.mutate(
      {
        file_id: selectedFile.id,
        name: newName
      },
      {
        onSuccess: () => {
          toast({
            title: "重命名成功",
            description: `文件已重命名为 ${newName}`,
          })
          setIsRenameDialogOpen(false)
        },
        onError: (error) => {
          toast({
            title: "重命名失败",
            description: "重命名文件时发生错误",
            variant: "destructive",
          })
        }
      }
    )
  }

  // 处理移动文件
  // const handleMoveFile = (file: FileItem) => {
  //   setSelectedFile(file)
  //   setIsMoveDialogOpen(true)
  // }

  // 处理确认移动
  // const handleConfirmMove = (targetFolderId: string | null) => {
  //   if (!selectedFile) return

  //   moveFile.mutate(
  //     {
  //       file_id: selectedFile.id,
  //       parentId: targetFolderId
  //     },
  //     {
  //       onSuccess: () => {
  //         toast({
  //           title: "移动成功",
  //           description: "文件已成功移动",
  //         })
  //         setIsMoveDialogOpen(false)
  //       },
  //       onError: (error) => {
  //         toast({
  //           title: "移动失败",
  //           description: "移动文件时发生错误",
  //           variant: "destructive",
  //         })
  //       }
  //     }
  //   )
  // }

  return (
    <CustomContainer
      title="文件管理"
      action={
        <AddFileDropdown
          onUploadFile={() => setIsUploadDialogOpen(true)}
        />
      }
    >
      {/* 面包屑导航 */}
      {/* <BreadcrumbNav
        currentPath={currentPath}
        onNavigate={handleNavigate}
      /> */}

      <div className="flex justify-between items-center mb-4">
        <div className="text-sm text-muted-foreground">
          {isLoading ? (
            "加载中..."
          ) : error ? (
            "加载失败"
          ) : (
            `共 ${filesData?.total || 0} 个项目，当前显示 ${currentFiles.length} 个`
          )}
        </div>
      </div>

      {/* 文件列表 */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <span className="ml-2">加载中...</span>
        </div>
      ) : error ? (
        <div className="text-center py-12 text-destructive">
          加载文件列表失败，请刷新页面重试
        </div>
      ) : (
        <FileList
          files={currentFiles}
          onDeleteFile={handleDeleteFile}
          onFolderClick={()=>{}}
          // onLinkKnowledgeBase={handleLinkKnowledgeBase}
          onRenameFile={handleRenameFile}
          // onMoveFile={()=>{}}
          // linkedKnowledgeBases={linkedKnowledgeBases}
        />
      )}

      {/* 上传文件对话框 */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>上传文件</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <FileUpload />
          </div>
        </DialogContent>
      </Dialog>

      {/* 创建文件夹对话框 */}
      {/* <CreateFolderDialog
        open={isCreateFolderDialogOpen}
        onClose={() => setIsCreateFolderDialogOpen(false)}
        onCreateFolder={handleCreateFolder}
        currentPath={currentPath}
      /> */}

      {/* 链接知识库对话框 */}
      {/* {selectedFile && (
        <LinkKnowledgeBaseDialog
          open={isLinkDialogOpen}
          onClose={() => setIsLinkDialogOpen(false)}
          onLink={handleConfirmLink}
          fileName={selectedFile.name}
          knowledgeBases={linkedKnowledgeBases[selectedFile.id] || []}
          linkedKnowledgeBaseIds={
            linkedKnowledgeBases[selectedFile.id]?.map(kb => kb.id) || []
          }
        />
      )} */}

      {/* 重命名对话框 */}
      {selectedFile && (
        <RenameFileDialog
          open={isRenameDialogOpen}
          onClose={() => setIsRenameDialogOpen(false)}
          onRename={handleConfirmRename}
          file={selectedFile}
        />
      )}

      {/* 移动文件对话框 */}
      {/* {selectedFile && (
        <MoveFileDialog
          open={isMoveDialogOpen}
          onClose={() => setIsMoveDialogOpen(false)}
          onMove={handleConfirmMove}
          file={selectedFile}
          folders={allFolders.filter(f => f.id !== selectedFile.id)}
          currentPath={currentPath}
        /> */}
      {/* )} */}
    </CustomContainer>
  )
}