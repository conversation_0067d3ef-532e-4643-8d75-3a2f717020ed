"use client"

import { useState } from "react"
import {
  File as FileIcon,
  FileText,
  FileSpreadsheet,
  FileCode,
  FileImage,
  FileArchive,
  FileAudio,
  FileVideo,
  FolderIcon,
  Link,
  Trash2
} from "lucide-react"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import { Button } from "@ragtop-web/ui/components/button"
import { Badge } from "@ragtop-web/ui/components/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ragtop-web/ui/components/alert-dialog"
import { formatFileSize } from "../utils"
import { formatDate } from "@/lib/utils"
import { FileActionMenu } from "./file-action-menu"
import { FileItem } from "@/service"

interface FileListProps {
  files: FileItem[]
  onDeleteFile: (fileId: string) => void
  onFolderClick: (folderId: string) => void
  // onLinkKnowledgeBase: (file: FileItem) => void
  onRenameFile: (file: FileItem) => void
  // onMoveFile: (file: FileItem) => void
  // linkedKnowledgeBases?: Record<string, KnowledgeBaseReference[]>
}

export function FileList({
  files,
  onDeleteFile,
  onFolderClick,
  // onLinkKnowledgeBase,
  onRenameFile,
  // onMoveFile,
}: FileListProps) {
  const [fileToDelete, setFileToDelete] = useState<FileItem | null>(null)

  // 获取文件图标
  const getFileIcon = (file: FileItem) => {
    if (file.isFolder) {
      return <FolderIcon className="h-4 w-4 text-yellow-500" />
    }

    switch (file.type.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-500" />
      case 'word':
        return <FileText className="h-4 w-4 text-blue-500" />
      case 'excel':
        return <FileSpreadsheet className="h-4 w-4 text-green-500" />
      case 'powerpoint':
        return <FileText className="h-4 w-4 text-orange-500" />
      case 'text':
        return <FileText className="h-4 w-4 text-gray-500" />
      case 'csv':
        return <FileSpreadsheet className="h-4 w-4 text-green-400" />
      case 'image':
        return <FileImage className="h-4 w-4 text-purple-500" />
      case 'archive':
        return <FileArchive className="h-4 w-4 text-yellow-500" />
      case 'video':
        return <FileVideo className="h-4 w-4 text-pink-500" />
      case 'audio':
        return <FileAudio className="h-4 w-4 text-indigo-500" />
      case 'code':
        return <FileCode className="h-4 w-4 text-cyan-500" />
      default:
        return <FileIcon className="h-4 w-4 text-gray-400" />
    }
  }

  // 处理删除文件
  const handleDelete = (file: FileItem) => {
    setFileToDelete(file)
    // if (!file.isFolder && file.references > 0) {
    //   // 如果文件被引用，显示确认对话框
    //   setFileToDelete(file)
    // } else {
    //   // 如果文件未被引用或是文件夹，直接删除
    //   onDeleteFile(file.id)
    // }
  }

  // 确认删除
  const confirmDelete = () => {
    if (fileToDelete) {
      onDeleteFile(fileToDelete.id)
      setFileToDelete(null)
    }
  }

  // 定义表格列
  const columns: ColumnDef<FileItem>[] = [
    {
      accessorKey: "name",
      header: "名称",
      cell: ({ row }) => {
        const file = row.original;
        return (
          <div
            className={`font-medium flex items-center gap-2 ${file.isFolder ? 'cursor-pointer hover:text-primary' : ''}`}
            onClick={() => file.isFolder && onFolderClick(file.id)}
          >
            {getFileIcon(file)}
            {file.name}
          </div>
        );
      },
    },
    {
      accessorKey: "type",
      header: "类型",
      cell: ({ row }) => (
        <Badge variant="outline">{row.original.type}</Badge>
      ),
    },
    {
      accessorKey: "size",
      header: "大小",
      cell: ({ row }) => formatFileSize(row.original.size),
    },
    {
      accessorKey: "link_num",
      header: "被链接数",
    },
    {
      accessorKey: "create_time",
      header: "上传日期",
      cell: ({ row }) => formatDate(row.original.create_time || ""),
    },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }) => {
        const file = row.original;
        return (
            <FileActionMenu
              file={file}
              // onLinkKnowledgeBase={onLinkKnowledgeBase}
              onRename={onRenameFile}
              // onMove={onMoveFile}
              onDelete={handleDelete}
            />
        );
      },
    },
  ];

  return (
    <>
      <DataTable columns={columns} data={files} />

      {/* 删除确认对话框 */}
      <AlertDialog open={!!fileToDelete} onOpenChange={(open) => !open && setFileToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除文件</AlertDialogTitle>
            <AlertDialogDescription>
              {fileToDelete?.link_num ?
             ` 该文件正在被 ${fileToDelete?.link_num} 个知识库链接。
              删除此文件将会影响这些知识库的内容。`:""}
              确定要删除吗？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
