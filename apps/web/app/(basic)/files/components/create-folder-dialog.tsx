"use client"

import { useState } from "react"
import { FolderPlus } from "lucide-react"
import { useForm } from "react-hook-form"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@ragtop-web/ui/components/dialog"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import { Label } from "@ragtop-web/ui/components/label"

interface CreateFolderDialogProps {
  open: boolean
  onClose: () => void
  onCreateFolder: (folderName: string) => void
  currentPath: string
}

interface FormValues {
  folderName: string
}

/**
 * 创建文件夹对话框组件
 */
export function CreateFolderDialog({
  open,
  onClose,
  onCreateFolder,
  currentPath,
}: CreateFolderDialogProps) {
  const { register, handleSubmit, formState: { errors }, reset } = useForm<FormValues>({
    defaultValues: {
      folderName: "",
    },
  })

  // 处理表单提交
  const onSubmit = (data: FormValues) => {
    onCreateFolder(data.folderName)
    reset()
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>新建文件夹</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="folderName">文件夹名称</Label>
              <Input
                id="folderName"
                placeholder="请输入文件夹名称"
                {...register("folderName", {
                  required: "文件夹名称不能为空",
                  pattern: {
                    value: /^[^\\/:*?"<>|]+$/,
                    message: "文件夹名称不能包含特殊字符 \\ / : * ? \" < > |",
                  },
                })}
              />
              {errors.folderName && (
                <p className="text-sm text-destructive">{errors.folderName.message}</p>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              当前位置: {currentPath === "/" ? "root" : currentPath}
            </div>
          </div>
          <DialogFooter className="mt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button type="submit">
              <FolderPlus className="mr-2 h-4 w-4" />
              创建
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
