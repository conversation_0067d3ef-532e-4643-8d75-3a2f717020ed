"use client"

import { useState } from "react"
import { FolderIcon, MoveIcon } from "lucide-react"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@ragtop-web/ui/components/dialog"
import { Button } from "@ragtop-web/ui/components/button"
import { ScrollArea } from "@ragtop-web/ui/components/scroll-area"
// 文件项类型
interface FileItem {
  id: string
  name: string
  type: string
  size: number
  link_num: number
  create_time: string
  isFolder: boolean
  parentId: string | null
  path: string
}

interface MoveFileDialogProps {
  open: boolean
  onClose: () => void
  onMove: (targetFolderId: string | null) => void
  file: FileItem | null
  folders: FileItem[]
  currentPath: string
}

/**
 * 移动文件对话框组件
 */
export function MoveFileDialog({
  open,
  onClose,
  onMove,
  file,
  folders,
  currentPath,
}: MoveFileDialogProps) {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null)

  // 处理文件夹选择
  const handleSelectFolder = (folderId: string | null) => {
    setSelectedFolderId(folderId)
  }

  // 处理提交
  const handleSubmit = () => {
    onMove(selectedFolderId)
    setSelectedFolderId(null)
    onClose()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>移动{file?.isFolder ? "文件夹" : "文件"}</DialogTitle>
        </DialogHeader>
        <div className="py-2">
          <p className="text-sm text-muted-foreground mb-4">
            选择要将 <span className="font-medium">{file?.name}</span> 移动到的位置
          </p>
          <ScrollArea className="h-[300px] rounded-md border p-4">
            <div className="space-y-2">
              <div
                className={`flex items-center p-2 rounded-md cursor-pointer ${
                  selectedFolderId === null ? "bg-accent" : "hover:bg-accent/50"
                }`}
                onClick={() => handleSelectFolder(null)}
              >
                <FolderIcon className="mr-2 h-4 w-4" />
                <span>根目录 (root)</span>
              </div>
              {folders
                .filter(folder => folder.id !== file?.id) // 排除当前文件/文件夹自身
                .map((folder) => (
                  <div
                    key={folder.id}
                    className={`flex items-center p-2 rounded-md cursor-pointer ${
                      selectedFolderId === folder.id ? "bg-accent" : "hover:bg-accent/50"
                    }`}
                    onClick={() => handleSelectFolder(folder.id)}
                  >
                    <FolderIcon className="mr-2 h-4 w-4" />
                    <span>{folder.name}</span>
                  </div>
                ))}
              {folders.length === 0 && (
                <p className="text-sm text-muted-foreground">暂无可用的文件夹</p>
              )}
            </div>
          </ScrollArea>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={handleSubmit}>
            <MoveIcon className="mr-2 h-4 w-4" />
            确认移动
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
