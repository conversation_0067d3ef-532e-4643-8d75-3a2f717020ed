"use client"

import { useMemo } from "react"
import { <PERSON>olderIcon, ChevronRight } from "lucide-react"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@ragtop-web/ui/components/breadcrumb"

interface BreadcrumbNavProps {
  currentPath: string
  onNavigate: (path: string) => void
}

/**
 * 面包屑导航组件
 */
export function BreadcrumbNav({ currentPath, onNavigate }: BreadcrumbNavProps) {
  // 解析当前路径为面包屑项
  const breadcrumbs = useMemo(() => {
    // 移除开头的斜杠并分割路径
    const pathWithoutLeadingSlash = currentPath.startsWith("/") 
      ? currentPath.substring(1) 
      : currentPath
    
    // 如果是根路径，只显示 "root"
    if (!pathWithoutLeadingSlash) {
      return [{ name: "root", path: "/" }]
    }
    
    // 分割路径并构建面包屑项
    const segments = pathWithoutLeadingSlash.split("/")
    const breadcrumbItems = segments.map((segment, index) => {
      // 构建到当前段的完整路径
      const path = "/" + segments.slice(0, index + 1).join("/")
      return {
        name: segment,
        path,
      }
    })
    
    // 在开头添加 "root"
    return [{ name: "root", path: "/" }, ...breadcrumbItems]
  }, [currentPath])

  return (
    <Breadcrumb className="mb-4">
      <BreadcrumbList>
        {breadcrumbs.map((item, index) => (
          <div key={item.path} className="flex items-center">
            {index > 0 && <BreadcrumbSeparator />}
            <BreadcrumbItem>
              {index === breadcrumbs.length - 1 ? (
                <BreadcrumbPage className="flex items-center">
                  <FolderIcon className="mr-1 h-4 w-4" />
                  {item.name}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink 
                  className="flex items-center cursor-pointer"
                  onClick={() => onNavigate(item.path)}
                >
                  <FolderIcon className="mr-1 h-4 w-4" />
                  {item.name}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
