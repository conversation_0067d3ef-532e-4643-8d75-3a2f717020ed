"use client"

import { useState, useRef } from "react"
import { Upload, X, AlertCircle, Refresh<PERSON>w, CheckCircle2 } from "lucide-react"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import { Progress } from "@ragtop-web/ui/components/progress"
import { Alert, AlertDescription } from "@ragtop-web/ui/components/alert"
import { Badge } from "@ragtop-web/ui/components/badge"
import {
  formatFileSize,
  validateFileType,
  validateFileSize,
  SUPPORTED_FILE_TYPES,
  MAX_FILE_COUNT,
  MAX_FILE_SIZE
} from "../utils"
import { useUploadFile } from "@/service/file-service"

// 文件上传状态
export type FileUploadStatus = 'pending' | 'uploading' | 'success' | 'error'

// 文件上传项
export interface FileUploadItem {
  file: File
  id: string
  status: FileUploadStatus
  progress: number
  error?: string
}

interface FileUploadProps {
  onUploadComplete?: (files: FileUploadItem[]) => void
  onUploadProgress?: (fileId: string, progress: number) => void
  onUploadError?: (fileId: string, error: string) => void
  uploadFunction?: (file: File) => Promise<any> // 可选的上传函数
}

export function FileUpload({ onUploadComplete, onUploadProgress, onUploadError }: FileUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<FileUploadItem[]>([])
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 使用真实的上传API
  const uploadFileMutation = useUploadFile()

  // 生成唯一ID
  const generateId = () => Math.random().toString(36).substring(2, 11)

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    const errors: string[] = []

    // 验证文件数量
    if (files.length > MAX_FILE_COUNT) {
      errors.push(`最多只能选择 ${MAX_FILE_COUNT} 个文件`)
      setValidationErrors(errors)
      return
    }

    // 验证每个文件
    const validFiles: FileUploadItem[] = []
    files.forEach(file => {
      // 验证文件类型
      if (!validateFileType(file)) {
        errors.push(`文件 "${file.name}" 类型不支持，支持的类型：${SUPPORTED_FILE_TYPES.join(', ')}`)
        return
      }

      // 验证文件大小
      if (!validateFileSize(file)) {
        errors.push(`文件 "${file.name}" 大小超过限制（最大 ${formatFileSize(MAX_FILE_SIZE)}）`)
        return
      }

      validFiles.push({
        file,
        id: generateId(),
        status: 'pending',
        progress: 0
      })
    })

    setValidationErrors(errors)
    setUploadFiles(validFiles)
  }

  // 处理单个文件上传
  const uploadSingleFile = async (fileItem: FileUploadItem): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 更新文件状态为上传中
      setUploadFiles(prev => prev.map(f =>
        f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f
      ))

      // 创建FormData
      const formData = new FormData()
      formData.append('file', fileItem.file)
      console.log(fileItem.file)
      uploadFileMutation.mutate(formData, {
        onSuccess: () => {
          setUploadFiles(prev => prev.map(f =>
            f.id === fileItem.id ? { ...f, status: 'success', progress: 100 } : f
          ))
          onUploadProgress?.(fileItem.id, 100)
          resolve()
        },
        onError: (error: any) => {
          const errorMessage = error?.message || '上传失败，请重试'
          setUploadFiles(prev => prev.map(f =>
            f.id === fileItem.id ? { ...f, status: 'error', progress: 0, error: errorMessage } : f
          ))
          onUploadError?.(fileItem.id, errorMessage)
          reject(error)
        }
      })
    })
  }

  // 处理批量上传
  const handleUpload = async () => {
    if (uploadFiles.length === 0) return

    try {
      // 并发上传所有文件
      await Promise.allSettled(
        uploadFiles.map(file => uploadSingleFile(file))
      )

      // 检查是否所有文件都上传成功
      const successFiles = uploadFiles.filter(f => f.status === 'success')
      if (successFiles.length > 0) {
        onUploadComplete?.(successFiles)
      }
    } catch (error) {
      console.error('批量上传失败:', error)
    }
  }

  // 重试上传失败的文件
  const handleRetry = (fileId: string) => {
    const fileItem = uploadFiles.find(f => f.id === fileId)
    if (fileItem) {
      uploadSingleFile(fileItem)
    }
  }

  // 移除文件
  const handleRemoveFile = (fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId))
  }

  // 清空所有文件
  const handleClearAll = () => {
    setUploadFiles([])
    setValidationErrors([])
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: FileUploadStatus) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'uploading':
        return <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
      default:
        return null
    }
  }

  // 获取状态文本
  const getStatusText = (file: FileUploadItem) => {
    switch (file.status) {
      case 'success':
        return '上传成功'
      case 'error':
        return file.error || '上传失败'
      case 'uploading':
        return `上传中 ${Math.round(file.progress)}%`
      default:
        return '等待上传'
    }
  }

  const hasUploadingFiles = uploadFiles.some(f => f.status === 'uploading')
  const hasErrorFiles = uploadFiles.some(f => f.status === 'error')
  const hasSuccessFiles = uploadFiles.some(f => f.status === 'success')

  return (
    <div className="space-y-4">
      {/* 文件选择区域 */}
      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
        <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            支持 {SUPPORTED_FILE_TYPES.join(', ')} 格式，单个文件最大 {formatFileSize(MAX_FILE_SIZE)}
          </p>
          <p className="text-xs text-muted-foreground">
            最多可选择 {MAX_FILE_COUNT} 个文件
          </p>
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={hasUploadingFiles}
          >
            选择文件
          </Button>
          <Input
            ref={fileInputRef}
            type="file"
            multiple
            accept={SUPPORTED_FILE_TYPES.map(type => `.${type}`).join(',')}
            className="hidden"
            onChange={handleFileChange}
          />
        </div>
      </div>

      {/* 验证错误提示 */}
      {validationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* 文件列表 */}
      {uploadFiles.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">已选择的文件 ({uploadFiles.length})</h4>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              disabled={hasUploadingFiles}
            >
              清空
            </Button>
          </div>

          <div className="space-y-2">
            {uploadFiles.map((file) => (
              <div key={file.id} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    {getStatusIcon(file.status)}
                    <span className="font-medium truncate">{file.file.name}</span>
                    <span className="text-sm text-muted-foreground">
                      ({formatFileSize(file.file.size)})
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {file.status === 'error' && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRetry(file.id)}
                      >
                        <RefreshCw className="h-4 w-4 mr-1" />
                        重试
                      </Button>
                    )}
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveFile(file.id)}
                      disabled={file.status === 'uploading'}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* 进度条 */}
                {file.status === 'uploading' && (
                  <Progress value={file.progress} className="h-2" />
                )}

                {/* 状态文本 */}
                <div className="text-xs text-muted-foreground">
                  {getStatusText(file)}
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-2">
            {!hasUploadingFiles && uploadFiles.some(f => f.status === 'pending' || f.status === 'error') && (
              <Button
                type="button"
                onClick={handleUpload}
              >
                {hasErrorFiles ? '重新上传' : '开始上传'}
              </Button>
            )}
            {hasSuccessFiles && !hasUploadingFiles && (
              <Badge variant="default" className="bg-green-50 text-green-700 border-green-200">
                {uploadFiles.filter(f => f.status === 'success').length} 个文件上传成功
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
