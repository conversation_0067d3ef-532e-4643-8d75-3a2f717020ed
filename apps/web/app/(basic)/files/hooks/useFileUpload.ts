/**
 * 文件上传Hook
 * 
 * 提供文件上传的统一逻辑，支持多文件上传、进度跟踪、错误处理和重试功能
 */

import { useState, useCallback } from 'react'
import { useUploadFile } from '@/service'
import { validateFileType, validateFileSize, MAX_FILE_COUNT } from '../utils'

export interface FileUploadItem {
  file: File
  id: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  error?: string
}

export interface UseFileUploadOptions {
  onSuccess?: (files: FileUploadItem[]) => void
  onError?: (error: string) => void
  onProgress?: (fileId: string, progress: number) => void
  maxFiles?: number
  autoUpload?: boolean
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const {
    onSuccess,
    onError,
    onProgress,
    maxFiles = MAX_FILE_COUNT,
    autoUpload = false
  } = options

  const [files, setFiles] = useState<FileUploadItem[]>([])
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const uploadFileMutation = useUploadFile()

  // 生成唯一ID
  const generateId = useCallback(() => {
    return Math.random().toString(36).substring(2, 11)
  }, [])

  // 验证文件
  const validateFiles = useCallback((fileList: File[]) => {
    const errors: string[] = []
    
    // 验证文件数量
    if (fileList.length > maxFiles) {
      errors.push(`最多只能选择 ${maxFiles} 个文件`)
      return { validFiles: [], errors }
    }
    
    const validFiles: FileUploadItem[] = []
    
    fileList.forEach(file => {
      // 验证文件类型
      if (!validateFileType(file)) {
        errors.push(`文件 "${file.name}" 类型不支持`)
        return
      }
      
      // 验证文件大小
      if (!validateFileSize(file)) {
        errors.push(`文件 "${file.name}" 大小超过限制`)
        return
      }
      
      validFiles.push({
        file,
        id: generateId(),
        status: 'pending',
        progress: 0
      })
    })
    
    return { validFiles, errors }
  }, [maxFiles, generateId])

  // 添加文件
  const addFiles = useCallback((fileList: File[]) => {
    const { validFiles, errors } = validateFiles(fileList)
    
    setValidationErrors(errors)
    setFiles(prev => [...prev, ...validFiles])
    
    // 如果启用自动上传且没有错误，则开始上传
    if (autoUpload && errors.length === 0 && validFiles.length > 0) {
      uploadFiles(validFiles)
    }
    
    return validFiles
  }, [validateFiles, autoUpload])

  // 上传单个文件
  const uploadSingleFile = useCallback(async (fileItem: FileUploadItem): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 更新文件状态为上传中
      setFiles(prev => prev.map(f => 
        f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f
      ))

      // 创建FormData
      const formData = new FormData()
      formData.append('file', fileItem.file)

      // 使用真实的上传API
      uploadFileMutation.mutate(formData, {
        onSuccess: () => {
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { ...f, status: 'success', progress: 100 } : f
          ))
          onProgress?.(fileItem.id, 100)
          resolve()
        },
        onError: (error: any) => {
          const errorMessage = error?.message || '上传失败，请重试'
          setFiles(prev => prev.map(f => 
            f.id === fileItem.id ? { ...f, status: 'error', progress: 0, error: errorMessage } : f
          ))
          onError?.(errorMessage)
          reject(error)
        }
      })

      // 模拟进度更新（实际项目中应该从API获取真实进度）
      const interval = setInterval(() => {
        setFiles(prev => prev.map(f => {
          if (f.id === fileItem.id && f.status === 'uploading') {
            const newProgress = Math.min(f.progress + Math.random() * 15, 95)
            onProgress?.(f.id, newProgress)
            return { ...f, progress: newProgress }
          }
          return f
        }))
      }, 200)

      // 清理定时器
      setTimeout(() => {
        clearInterval(interval)
      }, 5000)
    })
  }, [uploadFileMutation, onProgress, onError])

  // 批量上传文件
  const uploadFiles = useCallback(async (filesToUpload?: FileUploadItem[]) => {
    const targetFiles = filesToUpload || files.filter(f => f.status === 'pending' || f.status === 'error')
    
    if (targetFiles.length === 0) return

    try {
      // 并发上传所有文件
      await Promise.allSettled(
        targetFiles.map(file => uploadSingleFile(file))
      )

      // 检查上传结果
      const successFiles = files.filter(f => f.status === 'success')
      if (successFiles.length > 0) {
        onSuccess?.(successFiles)
      }
    } catch (error) {
      console.error('批量上传失败:', error)
      onError?.('批量上传失败')
    }
  }, [files, uploadSingleFile, onSuccess, onError])

  // 重试上传失败的文件
  const retryFile = useCallback((fileId: string) => {
    const fileItem = files.find(f => f.id === fileId)
    if (fileItem && fileItem.status === 'error') {
      uploadSingleFile(fileItem)
    }
  }, [files, uploadSingleFile])

  // 移除文件
  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId))
  }, [])

  // 清空所有文件
  const clearFiles = useCallback(() => {
    setFiles([])
    setValidationErrors([])
  }, [])

  // 获取统计信息
  const stats = {
    total: files.length,
    pending: files.filter(f => f.status === 'pending').length,
    uploading: files.filter(f => f.status === 'uploading').length,
    success: files.filter(f => f.status === 'success').length,
    error: files.filter(f => f.status === 'error').length,
    isUploading: files.some(f => f.status === 'uploading'),
    hasErrors: files.some(f => f.status === 'error'),
    hasSuccess: files.some(f => f.status === 'success')
  }

  return {
    files,
    validationErrors,
    stats,
    addFiles,
    uploadFiles,
    retryFile,
    removeFile,
    clearFiles,
    isUploading: uploadFileMutation.isPending
  }
}
