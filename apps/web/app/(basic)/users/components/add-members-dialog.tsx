"use client"

import { useState } from "react"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { SelectionList, type SelectionItem } from "@ragtop-web/ui/components/selection-list"
import { usePaginatedSearch } from "@ragtop-web/ui/hooks/use-paginated-search"
import { toast } from "sonner"
import { useAvailableMembersWithDynamicPaging } from "@/service/auth-service"

// 可添加的用户数据结构
interface AvailableUser {
  user_id: string
  name: string
  joined: boolean
}

interface AddMembersDialogProps {
  open: boolean
  onClose: () => void
  onAddMembers: (user_id: string ) => void
}

export function AddMembersDialog({
  open,
  onClose,
  onAddMembers,
}: AddMembersDialogProps) {
  const [selectedUserId, setSelectedUserId] = useState<string>("")

  // API hooks - 使用动态分页
  const availableMembersQuery = useAvailableMembersWithDynamicPaging()

  /**
   * 搜索函数 - 适配 usePaginatedSearch
   */
  const searchFunction = async (params: { pageNumber?: number; pageSize?: number; keyword?: string }) => {
    try {
      const response = await availableMembersQuery.mutateAsync({
        keyword: params.keyword || undefined,
        pageNumber: params.pageNumber || 1,
        pageSize: params.pageSize || 10
      })

      // 转换数据格式
      const formattedUsers = response?.records?.map((item: any) => ({
        user_id: item.user?.id || "",
        name: item.user?.nick || item.user?.username || "未知用户",
        joined: item.joined,
      })) || []

      return {
        records: formattedUsers,
        total: response?.total || 0
      }
    } catch (error) {
      console.error("获取可添加成员失败:", error)
      toast.error("获取可添加成员失败")
      return { records: [], total: 0 }
    }
  }

  // 使用分页搜索 Hook
  const {
    data: availableUsers,
    isLoading,
    hasMore,
    searchTerm,
    setSearchTerm,
    handleScroll,
    reset
  } = usePaginatedSearch<AvailableUser>({
    searchFn: searchFunction,
    pageSize: 10,
    debounceDelay: 300,
    enabled: open
  })

  // 转换数据为 SelectionItem 格式
  const selectionItems: SelectionItem[] = availableUsers.map(user => ({
    id: user.user_id,
    label: user.name,
    disabled: user.joined // joined 为 true 时禁用选择
  }))

  // 处理选择变化
  const handleSelectionChange = (selectedIds: string | string[]) => {
    if (typeof selectedIds === "string") {
      setSelectedUserId(selectedIds)
    }
  }

  // 处理添加成员
  const handleAddMembers = () => {
    onAddMembers(selectedUserId)
    handleClose()
  }

  // 处理关闭对话框
  const handleClose = () => {
    setSelectedUserId("")
    reset() // 重置分页搜索状态
    onClose()
  }

  return (
    <CustomDrawer
      open={open}
      onClose={handleClose}
      title="添加团队成员"
      footer={
        <Button
          onClick={handleAddMembers}
          disabled={selectedUserId === ""}
        >
          添加成员
        </Button>
      }
    >
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">
          从现有账户中选择要添加到团队的成员
        </p>

        <SelectionList
          items={selectionItems}
          isLoading={isLoading}
          hasMore={hasMore}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onScroll={handleScroll}
          selectionMode="single"
          selectedIds={selectedUserId}
          onSelectionChange={handleSelectionChange}
          searchPlaceholder="搜索成员..."
          emptyText="未找到匹配的成员"
          loadingText="加载中..."
          endText="已到达列表底部"
          height="300px"
        />

        {/* 已选择提示 */}
        {selectedUserId !== "" && (
          <div className="text-sm text-muted-foreground">
            已选择 1 名成员
          </div>
        )}
      </div>
    </CustomDrawer>
  )
}
