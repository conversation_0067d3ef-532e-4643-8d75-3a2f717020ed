"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import { Alert, AlertDescription, AlertTitle } from "@ragtop-web/ui/components/alert"
import { type Dataset, useTestConnection } from "@/service/dataset-service"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(1,"数据集名称不能为空"),
  description: z.string().min(1, "数据集描述不能为空"),
  username: z.string().min(1, "用户名不能为空"),
  password: z.string().min(1, "密码不能为空"),
  database: z.string().min(1, "数据库名称不能为空"),
})

type FormValues = z.infer<typeof formSchema>

interface DatasetFormProps {
  dataset?: Dataset
  onSave: (formData: FormValues) => void
  isCreating: boolean
}

export function DatasetForm({ dataset, onSave, isCreating }: DatasetFormProps) {
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<"success" | "error" | null>(null)
  const [testMessage, setTestMessage] = useState("")

  // 测试连接API hook
  const testConnectionMutation = useTestConnection()

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: dataset
      ? {
          name: dataset.name || "",
          description: dataset.description || "",
          username: dataset.meta?.username || "",
          password: "",
          database: dataset.meta?.database || "",
        }
      : {
          name: "",
          description: "",
          username: "",
          password: "",
          database: "",
        },
  })

  // 测试连接
  const handleTestConnection = async () => {
    const values = form.getValues()
    const isValid = await form.trigger()

    if (!isValid) {
      return
    }

    setIsTesting(true)
    setTestResult(null)
    setTestMessage("")

    testConnectionMutation.mutate({
      database: values.database,
      username: values.username,
      password: values.password,
    }, {
      onSuccess: () => {
        setTestResult("success")
          setTestMessage("连接成功！数据库可以正常访问。")
        setIsTesting(false)
      },
      onError: () => {
        setTestResult("error")
        setTestMessage("发生错误，请稍后重试。")
        setIsTesting(false)
      }
    })
  }

  // 保存数据集
  const handleSubmit = (values: FormValues) => {
    if (testResult !== "success") {
      setTestMessage("请先测试连接并确保连接成功")
      return
    }

    onSave(values)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据集名称</FormLabel>
              <FormControl>
                <Input placeholder="输入数据集名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据集描述</FormLabel>
              <FormControl>
                <Input placeholder="输入数据集描述" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="database"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据库</FormLabel>
              <FormControl>
                <Input placeholder="输入数据库" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据库用户名</FormLabel>
              <FormControl>
                <Input placeholder="输入用户名" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数据库密码</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={"输入密码"}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {testResult && (
          <Alert variant={testResult === "success" ? "default" : "destructive"}>
            {testResult === "success" ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertTitle>
              {testResult === "success" ? "连接成功" : "连接失败"}
            </AlertTitle>
            <AlertDescription>{testMessage}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleTestConnection}
            disabled={isTesting}
            className="flex-1"
          >
            {isTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            测试连接
          </Button>

          <Button
            type="submit"
            disabled={testResult !== "success"}
            className="flex-1"
          >
            保存
          </Button>
        </div>
      </form>
    </Form>
  )
}
