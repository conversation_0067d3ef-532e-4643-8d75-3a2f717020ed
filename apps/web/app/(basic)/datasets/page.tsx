"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { PlusIcon, RefreshCw, ChevronLeft, ChevronRight } from "lucide-react"
import { Card, CardContent } from "@ragtop-web/ui/components/card"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { DatasetCard } from "./components/dataset-card"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { DatasetForm } from "./components/dataset-form"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import {
  useDatasets,
  useCreateDataset,
  useUpdateDataset,
  useBuildDataset,
  type Dataset,
  type CreateDatasetParams,
  type UpdateDatasetParams
} from "@/service/dataset-service"

export default function DatasetsPage() {
  const { toast } = useToast()
  const router = useRouter()
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)

  // 使用dataset-service获取数据集列表
  const {
    data: datasetsResponse,
    isLoading,
    error,
    refetch
  } = useDatasets(currentPage, pageSize, true)

  // 从响应中提取数据集列表
  const datasets = datasetsResponse?.records || []
  const total = datasetsResponse?.total || 0
  const totalPages = Math.ceil(total / pageSize)

  // 数据集操作hooks
  const createDataset = useCreateDataset()
  const updateDataset = useUpdateDataset()
  const buildDataset = useBuildDataset()

  // 处理打开数据集详情页面
  const handleOpenDataset = (dataset: Dataset) => {
    router.push(`/datasets/${dataset.id}`)
  }

  // 处理打开创建数据集抽屉
  const handleCreateDataset = () => {
    setSelectedDataset(null)
    setIsCreating(true)
    setIsDrawerOpen(true)
  }

  // 处理编辑数据集
  const handleEditDataset = (dataset: Dataset, e: React.MouseEvent) => {
    e.stopPropagation()
    setSelectedDataset(dataset)
    setIsCreating(false)
    setIsDrawerOpen(true)
  }

  // 处理关闭抽屉
  const handleCloseDrawer = () => {
    setIsDrawerOpen(false)
  }

  // 处理保存数据集
  const handleSaveDataset = (datasetData: CreateDatasetParams | UpdateDatasetParams) => {
    if (isCreating) {
      // 创建新数据集
      createDataset.mutate(datasetData as CreateDatasetParams, {
        onSuccess: () => {
          toast({
            title: "创建成功",
            description: "数据集已创建",
          })
          setIsDrawerOpen(false)
          // 创建成功后刷新列表，useDatasets会自动通过queryClient.invalidateQueries刷新
        },
        onError: () => {
          toast({
            title: "创建失败",
            description: "创建数据集时发生错误",
            variant: "destructive",
          })
        }
      })
    } else if (selectedDataset) {
      // 更新现有数据集
      updateDataset.mutate(
        {
          ...datasetData,
          tableset_id: selectedDataset.id
        } as UpdateDatasetParams,
        {
          onSuccess: () => {
            toast({
              title: "更新成功",
              description: "数据集已更新",
            })
            setIsDrawerOpen(false)
            // 更新成功后刷新列表，useDatasets会自动通过queryClient.invalidateQueries刷新
          },
          onError: () => {
            toast({
              title: "更新失败",
              description: "更新数据集时发生错误",
              variant: "destructive",
            })
          }
        }
      )
    }
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // 处理构建数据集
  const handleBuildDataset = (dataset: Dataset, e: React.MouseEvent) => {
    e.stopPropagation()

    buildDataset.mutate(
      { tableset_id: dataset.id || "" },
      {
        onSuccess: () => {
          toast({
            title: "构建成功",
            description: "数据集已开始构建",
          })
        },
        onError: () => {
          toast({
            title: "构建失败",
            description: "构建数据集时发生错误",
            variant: "destructive",
          })
        }
      }
    )
  }



  return (
    <CustomContainer title="数据集管理">
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <span className="ml-2">加载中...</span>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <div className="text-destructive mb-4">
            加载数据集列表失败: {error.message || '未知错误'}
          </div>
          <Button
            variant="outline"
            onClick={() => refetch()}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            重试
          </Button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 添加数据集卡片 */}
            <Card
              className="border-dashed cursor-pointer hover:border-primary/50 transition-colors"
              onClick={handleCreateDataset}
            >
              <CardContent className="flex items-center justify-center h-20">
                <div className="flex flex-col items-center text-muted-foreground">
                  <PlusIcon className="h-10 w-10 mb-2" />
                  <span className="text-lg">添加新数据集</span>
                </div>
              </CardContent>
            </Card>

            {/* 数据集卡片列表 */}
            {datasets.map((dataset) => (
              <DatasetCard
                key={dataset.id}
                dataset={dataset}
                onClick={() => handleOpenDataset(dataset)}
                onBuild={handleBuildDataset}
                onEdit={handleEditDataset}
              />
            ))}
          </div>

          {/* 分页控制 */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center gap-4 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>

              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <span className="text-sm text-muted-foreground">
                  (共 {total} 条记录)
                </span>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="flex items-center gap-2"
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* 空状态 */}
          {!isLoading && datasets.length === 0 && (
            <div className="text-center py-12 text-muted-foreground">
              暂无数据集，点击上方按钮创建第一个数据集
            </div>
          )}
        </>
      )}

      {/* 数据集抽屉 */}
      <CustomDrawer
        open={isDrawerOpen}
        onClose={handleCloseDrawer}
        title={isCreating ? "创建数据集" : "数据集详情"}
      >
        <DatasetForm
          dataset={isCreating ? undefined : selectedDataset!}
          onSave={(formData) => {
            const apiData = isCreating ? {
              name: formData.name,
              description: formData.description,
              database: formData.database,
              username: formData.username,
              password: formData.password
            } as CreateDatasetParams : {
              name: formData.name,
              description: formData.description,
              tableset_id: selectedDataset?.id || ""
            } as UpdateDatasetParams
            handleSaveDataset(apiData)
          }}
          isCreating={isCreating}
        />
      </CustomDrawer>
    </CustomContainer>
  )
}
