"use client"

import { useState } from "react"
import { ChevronRight, Database, Table } from "lucide-react"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@ragtop-web/ui/components/collapsible"
import { Input } from "@ragtop-web/ui/components/input"
import { ScrollArea } from "@ragtop-web/ui/components/scroll-area"
import { cn } from "@ragtop-web/ui/lib/utils"

// 表结构类型
export interface TableSchema {
  id: string
  name: string
  tables: TableItem[]
}

export interface TableItem {
  id: string
  name: string
  schemaId: string
}

interface TableTreeProps {
  schemas: TableSchema[]
  selectedTable: TableItem | null
  onSelectTable: (table: TableItem) => void
}

export function TableTree({ schemas, selectedTable, onSelectTable }: TableTreeProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [expandedSchemas, setExpandedSchemas] = useState<Record<string, boolean>>(
    schemas.reduce((acc, schema) => ({ ...acc, [schema.id]: true }), {})
  )

  // 切换模式展开/折叠状态
  const toggleSchema = (schemaId: string) => {
    setExpandedSchemas(prev => ({
      ...prev,
      [schemaId]: !prev[schemaId]
    }))
  }

  // 过滤表格
  const filteredSchemas = schemas.map(schema => ({
    ...schema,
    tables: schema.tables.filter(table => 
      table.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(schema => schema.tables.length > 0 || schema.name.toLowerCase().includes(searchTerm.toLowerCase()))

  return (
    <div className="flex flex-col h-full border rounded-md">
      <div className="p-2 border-b">
        <Input
          placeholder="搜索..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="h-8"
        />
      </div>
      <ScrollArea className="flex-1">
        <div className="p-2">
          {filteredSchemas.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              未找到匹配的表格
            </div>
          ) : (
            filteredSchemas.map((schema) => (
              <Collapsible
                key={schema.id}
                defaultOpen={expandedSchemas[schema.id]}
                onOpenChange={() => toggleSchema(schema.id)}
              >
                <div className="flex items-center py-1">
                  <CollapsibleTrigger className="flex items-center gap-1 hover:text-primary transition-colors">
                    <ChevronRight 
                      className={cn(
                        "h-4 w-4 transition-transform", 
                        expandedSchemas[schema.id] && "rotate-90"
                      )} 
                    />
                    <Database className="h-4 w-4" />
                    <span className="text-sm font-medium">{schema.name}</span>
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent>
                  <div className="ml-6 space-y-1 mt-1">
                    {schema.tables.map((table) => (
                      <div
                        key={table.id}
                        className={cn(
                          "flex items-center gap-1 py-1 px-2 rounded-md text-sm cursor-pointer hover:bg-muted transition-colors",
                          selectedTable?.id === table.id && "bg-muted font-medium"
                        )}
                        onClick={() => onSelectTable(table)}
                      >
                        <Table className="h-4 w-4" />
                        <span>{table.name}</span>
                      </div>
                    ))}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
