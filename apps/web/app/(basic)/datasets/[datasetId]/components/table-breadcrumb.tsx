"use client"

import { Database, Table, ChevronRight } from "lucide-react"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@ragtop-web/ui/components/breadcrumb"
import { type TableSchema, type TableItem } from "./table-tree"

interface TableBreadcrumbProps {
  schema: TableSchema | null
  table: TableItem | null
  onNavigate: (type: "schema" | "table", id: string) => void
}

export function TableBreadcrumb({ schema, table, onNavigate }: TableBreadcrumbProps) {
  return (
    <Breadcrumb className="mb-4">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink 
            className="flex items-center cursor-pointer"
            onClick={() => onNavigate("schema", "schemas")}
          >
            <span>Schemas</span>
          </BreadcrumbLink>
        </BreadcrumbItem>
        
        {schema && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink 
                className="flex items-center cursor-pointer"
                onClick={() => onNavigate("schema", schema.id)}
              >
                <Database className="mr-1 h-4 w-4" />
                {schema.name}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </>
        )}
        
        {table && (
          <>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="flex items-center">
                <Table className="mr-1 h-4 w-4" />
                {table.name}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
