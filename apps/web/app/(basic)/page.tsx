"use client";
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useIsTeamAdmin } from "@/lib/user-role"

/**
 * 首页组件
 * 
 * 显示系统概览和快速访问链接
 */
export default function HomePage() {
  const isAdmin = useIsTeamAdmin()

  
  return (
    <CustomContainer title="欢迎使用 Ragtop">
      <div className="space-y-8">
        {/* 欢迎信息 */}
        <div className="bg-muted/30 p-6 rounded-lg">
          <h2 className="text-2xl font-bold mb-2">欢迎回来</h2>
          <p className="text-muted-foreground">
            Ragtop 是一个强大的知识库和数据集管理平台，帮助您更高效地管理和利用数据资源。
            {isAdmin ? " 作为团队管理员，您可以管理团队成员和配置模型。" : " 您可以访问团队共享的资源和创建个人资源。"}
          </p>
        </div>
      </div>
    </CustomContainer>
  )
}
