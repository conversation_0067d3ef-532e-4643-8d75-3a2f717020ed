/**
 * Agent服务
 *
 * 提供Agent相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createWebApiClient, PaginatedResponse } from "@/lib/api/web-api-client";
// 导出API前缀常量
const API_PREFIX = process.env.NEXT_PUBLIC_API_PREFIX || '/api/v1/portal-ragtop'
import { ResType } from "./types";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${API_PREFIX}/agent`,
});

export interface AgentSettings {
  agent_prompt: string;
  keyword_weight: number;
  llm_model: string;
  similarity_threshold: number;
  temperature: number;
  top_n: number;
  top_p: number;
}

export interface Agents {
  create_time: number;
  id: string;
  description?: string;
  owner_id: string;
  // resource_ids: string[];
  // settings: AgentSettings;
  scope?: Scope;
  name: string;
  type?: Type;
}


/**
 * CreateAgentRequest
 */
export interface AgentCreateParams {
    abnormal_message?: string;
    description?: string;
    hello_message?: string;
    link_resources?: string[];
    name?: string;
    prompt?: string;
    scope?: Scope;
    settings?: TeamAgentSettings;
    team_id?: string;
    type?: Type;
    [property: string]: any;
}

export interface AgentDetailsParams {
    abnormal_message?: string;
    description?: string;
    hello_message?: string;
    link_resources?: Resource[];
    name?: string;
    prompt?: string;
    scope?: Scope;
    settings?: TeamAgentSettings;
    team_id?: string;
    type?: Type;
    [property: string]: any;
}

export interface Resource {
  res_id:string,
  res_name:string
  res_type:ResType
}

/**
 * 使用范围
 */
export enum Scope {
    Private = "PRIVATE",
    TeamPublic = "TEAM_PUBLIC",
}

/**
 * 助手类型
 */
export enum Type {
    NlToSQL = "NL_TO_SQL", // 数据集
    Rag = "RAG", // 知识库
}


/**
 * TeamAgentSettings
 */
export interface TeamAgentSettings {
    /**
     * 自由度
     */
    flexibility?: Flexibility;
    keyword_weight?: number;
    llm_model?: string;
    llm_model_name?:string;
    similarity_threshold?: number;
    temperature?: number;
    top_n?: number;
    top_p?: number;
    [property: string]: any;
}

/**
 * 自由度
 */
export enum Flexibility {
    Balanced = "BALANCED",
    Deliberate = "DELIBERATE",
    Improvised = "IMPROVISED",
}

export const settledModelVariableMap = {
  [Flexibility.Improvised]: {
    temperature: 0.9,
    top_p: 0.9,
    // frequency_penalty: 0.2,
    // presence_penalty: 0.4,
    // max_tokens: 512,
  },
  [Flexibility.Deliberate]: {
    temperature: 0.1,
    top_p: 0.3,
    // frequency_penalty: 0.7,
    // presence_penalty: 0.4,
    // max_tokens: 512,
  },
  [Flexibility.Balanced]: {
    temperature: 0.5,
    top_p: 0.5,
    // frequency_penalty: 0.7,
    // presence_penalty: 0.4,
    // max_tokens: 512,
  },
};

/**
 * 获取Agent列表
 * 分页查询
 */
export const useAgents = (scope:Scope,pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ["agents",scope, pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<Agents>>("/query", {scope}, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled,
  });
};

/**
 * 获取单个Agent
 */
export const useAgent = (id:string, enabled = true) => {
  return useQuery({
    queryKey: ["agent_details",id],
    queryFn: () => apiClient.post<AgentDetailsParams>("/describe", {agent_id:id}),
    enabled: !!id && enabled,
  })
};

/**
 * 创建Agent
 */
export const useCreateAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AgentCreateParams) =>
      apiClient.post<Agents>("/create", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
  });
};

/**
 * 更新Agent
 */
export const useUpdateAgent = (id:string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data : Partial<AgentCreateParams> & { agent_id: string }) =>
      apiClient.post<Agents>(`/modify`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents", id] });
      queryClient.invalidateQueries({ queryKey: ["agent_details", id] });
    },
  });
};

/**
 * 删除Agent
 */
export const useDeleteAgent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: { agent_id: string }) => apiClient.post(`/delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
  });
};

/**
 * 获取Agent会话列表
 * 分页查询
 */
export const useAgentSessions = (agentId: string, pageNumber = 1, pageSize = 10) => {
  return useQuery({
    queryKey: ["agents", agentId, "sessions", pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<any>>(`/sessions/query`, { agent_id: agentId }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled: !!agentId,
  });
};

/**
 * 获取会话消息列表
 * 分页查询
 */
export const useSessionMessages = (sessionId: string, pageNumber = 1, pageSize = 20) => {
  return useQuery({
    queryKey: ["sessions", sessionId, "messages", pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<any>>(`/messages/query`, { session_id: sessionId }, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled: !!sessionId,
  });
};

/**
 * 发送消息
 */
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      sessionId,
      content,
    }: {
      sessionId: string;
      content: string;
    }) =>
      apiClient.post<any>(`/messages/create`, { session_id: sessionId, content }),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["sessions", variables.sessionId, "messages"],
      });
    },
  });
};
