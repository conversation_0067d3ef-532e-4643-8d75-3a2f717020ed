/**
 * 知识库服务
 *
 * 提供知识库相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { createWebApiClient, PaginatedResponse } from '@/lib/api/web-api-client'
// 导出API前缀常量
const API_PREFIX = process.env.NEXT_PUBLIC_API_PREFIX || '/api/v1/portal-ragtop'
import { ResType } from './types'

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${API_PREFIX}/kbase`,
})

// 切片方法类型
export type SliceMethodType =
  | "naive"
  | "qa"
  | "resume"
  | "manual"
  | "table"
  | "paper"
  | "book"
  | "laws"
  | "presentation"
  | "picture"
// | "one"
// | "audio"
// | "email"
// | "tag"
// |"knowledgeGraph"




// 知识库接口
export interface KnowledgeBase {
    create_time?: Date;
    description?: string;
    doc_num?: number;
    id: string;
    infra_res_id?: string;
    name: string;
    res_type?: ResType;
    [property: string]: any;

}



/**
 * KbaseChunkConfig
 */
export interface KbaseChunkConfig {
    chunk_provider_id?: SliceMethodType;
    [property: string]: any;
}

/**
 * KbasePdfParserConfig
 */
export interface KbasePdfParserConfig {
    parser_provider_id?: string;
    [property: string]: any;
}

/**
 * CreateKbaseRequest
 */
export interface KnowledgeBaseParams {
    chunk_config?: KbaseChunkConfig;
    description?: string;
    name?: string;
    pdf_parser_config?: KbasePdfParserConfig;
    teamid?: string;
    [property: string]: any;
}

// 单个知识库详情

export interface KbaseRes {
    chunk_config?: KbaseChunkConfig;
    create_time?: Date;
    description?: string;
    doc_num?: number;
    id?: string;
    infra_res_id?: string;
    name?: string;
    pdf_parser_config?: KbasePdfParserConfig;
    /**
     * 资源类型
     */
    res_type?: ResType;
    [property: string]: any;
}


// 文档接口
export interface Document {
  id: string
  name: string
  type: string
  size: string
  parseStatus: 'parsed' | 'parsing' | 'failed' | 'none'
  enabled?: boolean
  knowledgeBaseId: string
  createdAt: string
}



/**
 * 获取知识库列表
 * 分页查询
 */
export const useKnowledgeBases = (pageNumber = 1, pageSize = 10, enabled = true) => {
  return useQuery({
    queryKey: ['knowledgeBases', pageNumber, pageSize],
    queryFn: () => apiClient.post<PaginatedResponse<KnowledgeBase>>('/query', {}, {
      isPaginated: true,
      pageNumber,
      pageSize
    }),
    enabled,
  })
}

/**
 * 获取单个知识库
 */
export const useKnowledgeBase = (id:string, enabled = true) => {
  return useQuery({
    queryKey: ['knowledgeBases',id],
    queryFn: () => apiClient.post<KbaseRes>('/describe', {kbase_id:id}),
    enabled: !!id && enabled
  })

}

/**
 * 创建知识库
 */
export const useCreateKnowledgeBase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: KnowledgeBaseParams) =>
      apiClient.post<KnowledgeBase>("/create", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeBases'] })
    },
  })
}

/**
 * 更新知识库
 */
export const useUpdateKnowledgeBase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: KnowledgeBaseParams & { kbase_id: string }) =>
      apiClient.post<KnowledgeBase>(`/modify`, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeBases'] })
      queryClient.invalidateQueries({ queryKey: ['knowledgeBase', data.kbase_id] })
    },
  })
}

/**
 * 删除知识库
 */
export const useDeleteKnowledgeBase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { kbase_id: string }) => apiClient.post(`/delete`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeBases'] })
    },
  })
}

/**
 * 向知识库添加文档
 */
export const useAddKnowledgeBaseDocuments = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: { kbase_id: string, file_ids: string[] }) =>
      apiClient.post(`/add-team-files`, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['knowledgeBases', variables.kbase_id] })
      queryClient.invalidateQueries({ queryKey: ["kbaseDoc"] });
    },
  })
}
